package com.desaysv.workserver.devices.android;

import com.desaysv.workserver.constants.AdbConstants;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.response.ResultEntity;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.utils.NetworkUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.Frame;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Android设备
 */
@Slf4j
public abstract class AndroidDevice extends Device implements IAndroid {

    @Getter
    private final String deviceType = DeviceType.DEVICE_ANDROID;
    private final RestTemplate restTemplateClient = new RestTemplate();
    private Process videoStreamProcess; // 用于管理视频流进程

    public AndroidDevice() {
        this(new DeviceOperationParameter());
    }

    public AndroidDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        GrabRequest grabRequest = new GrabRequest();
        grabRequest.setDeviceUUID(getDeviceUniqueCode());
        grabRequest.setDeviceName(getDeviceName());
        grabRequest.setDeviceModel(getDeviceModel());
//        grabRequest.setDevicePort(getDevicePort());
        DeviceOperationParameter operationParameter = getDeviceOperationParameter();
        try {
            grabRequest.setWidth(operationParameter.getWidth());
            grabRequest.setHeight(operationParameter.getHeight());
        } catch (OperationParameterExtractException e) {
            log.warn("获取Android分辨率参数失败:{}", e.getMessage());
        }
        ParameterizedTypeReference<ResultEntity<Object>> typeRef = new ParameterizedTypeReference<ResultEntity<Object>>() {
        };
        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/grab", NetworkUtils.getServerPort());
        ResultEntity<Object> entity = restTemplateClient.exchange(url, HttpMethod.POST, new HttpEntity<>(grabRequest), typeRef).getBody();
//        throw new OperationFailException();
        if (entity == null) {
            throw new DeviceOpenException(String.format("Post请求失败:%s", url));
        }
        if (!entity.isOk()) {
            throw new DeviceOpenException(entity.getMessage());
        }
        return entity.isOk();
    }


    public static List<AdbStatus> getAllAndroids() {
        List<AdbStatus> adbStatusList = new ArrayList<>();
        try {
            List<String> adbDevicesResult = CommandUtils.executeCommandToArray(AdbConstants.Command.QUERY_ADB_DEVICES);
            log.info("adbDevicesResult:{}", adbDevicesResult);
            if (!adbDevicesResult.isEmpty()) {
                adbDevicesResult.remove(0);
            }
            for (String status : adbDevicesResult) {
                String[] statusArray = status.split("\t");
                AdbStatus adbStatus = new AdbStatus();
                adbStatus.setSerialNumber(statusArray[0]);
                adbStatus.setStatus(statusArray[1]);
                adbStatusList.add(adbStatus);
            }
        } catch (IOException e) {
            log.warn(e.getMessage(), e);
        }
        return adbStatusList;
    }

    public String wrapAdbCommand(String command) {
        return command.replaceAll("adb", String.format("adb -s %s", getDeviceName()));
    }

    /**
     * 启动 Android 设备的视频流。
     *
     * @param width   视频宽度。如果为0或负数，则使用设备默认宽度。
     * @param height  视频高度。如果为0或负数，则使用设备默认高度。
     * @param bitRate 视频比特率 (例如 4000000 表示 4Mbps)。如果为0或负数，则使用默认比特率。
     * @return 视频流的 InputStream。
     * @throws IOException 如果执行 ADB 命令失败。
     */
    public synchronized InputStream startVideoStream(int width, int height, int bitRate) throws IOException {
        if (isVideoStreamRunning()) {
            log.info("视频流已在运行，将先停止现有视频流。");
            stopVideoStream();
        }

        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        List<String> commandParts = new ArrayList<>();
        commandParts.add("adb"); // 直接使用 "adb"
        commandParts.add("-s");
        commandParts.add(deviceSerial);
        commandParts.add("exec-out");
        commandParts.add("screenrecord");
        commandParts.add("--output-format=h264");

        if (width > 0 && height > 0) {
            commandParts.add("--size");
            commandParts.add(width + "x" + height);
        }
        if (bitRate > 0) {
            commandParts.add("--bit-rate");
            commandParts.add(String.valueOf(bitRate));
        }
        commandParts.add("-"); // 输出到 stdout

        ProcessBuilder processBuilder = new ProcessBuilder(commandParts);
        log.info("执行视频流命令: {}", String.join(" ", commandParts));
        try {
            videoStreamProcess = processBuilder.start();
            // 检查进程是否成功启动 (可选, 但有助于调试)
            if (!videoStreamProcess.isAlive()) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(videoStreamProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                videoStreamProcess = null; // 重置，因为启动失败
                throw new IOException("启动视频流进程失败。错误信息: " + errorOutput.toString().trim());
            }
            log.info("视频流已成功启动。设备: {}", deviceSerial);
            return videoStreamProcess.getInputStream();
        } catch (IOException e) {
            log.error("启动视频流失败。设备: {}, 命令: {}. 错误: {}", deviceSerial, String.join(" ", commandParts), e.getMessage());
            videoStreamProcess = null; // 确保在失败时重置
            throw e;
        }
    }

    /**
     * 停止 Android 设备的视频流。
     */
    public synchronized void stopVideoStream() {
        if (videoStreamProcess != null && videoStreamProcess.isAlive()) {
            log.info("正在停止视频流... 设备: {}", getDeviceName());
            videoStreamProcess.destroyForcibly();
            try {
                if (!videoStreamProcess.waitFor(5, TimeUnit.SECONDS)) {
                    log.warn("视频流进程在5秒内未终止。设备: {}", getDeviceName());
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("等待视频流进程终止时被中断。设备: {}", getDeviceName(), e);
            }
            log.info("视频流已停止。设备: {}", getDeviceName());
        }
        videoStreamProcess = null; // 清理引用
    }

    /**
     * 检查视频流是否正在运行。
     *
     * @return 如果视频流进程存在且活动，则返回 true。
     */
    public synchronized boolean isVideoStreamRunning() {
        return videoStreamProcess != null && videoStreamProcess.isAlive();
    }

    /**
     * 获取当前 Android 设备 UI 层级结构的 XML 字符串。
     *
     * @return UI 层级结构的 XML 字符串。
     * @throws IOException 如果执行 ADB 命令失败或读取输出时发生错误。
     */
    public String getUIHierarchyXml() throws IOException {
        String deviceSerial = getDeviceName();
        if (deviceSerial == null || deviceSerial.isEmpty()) {
            throw new IOException("设备序列号/名称不可用。");
        }

        // 清理旧的 dump 文件 (可选, 但推荐)
        try {
            Process clearProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "rm", "/sdcard/window_dump.xml").start();
            if (!clearProcess.waitFor(5, TimeUnit.SECONDS)) {
                clearProcess.destroyForcibly();
                log.warn("清理 /sdcard/window_dump.xml 超时。");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("清理 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            // 忽略清理错误，可能文件不存在
            log.debug("清理 /sdcard/window_dump.xml 时出错 (可能文件不存在): {}", e.getMessage());
        }

        // 执行 uiautomator dump
        Process dumpProcess = null;
        try {
            dumpProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "uiautomator", "dump", "/sdcard/window_dump.xml").start();
            if (!dumpProcess.waitFor(15, TimeUnit.SECONDS)) { // UI dump 可能需要一些时间
                dumpProcess.destroyForcibly();
                throw new IOException("执行 uiautomator dump 超时。");
            }
            int exitCode = dumpProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(dumpProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("执行 uiautomator dump 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            if (dumpProcess != null) {
                dumpProcess.destroyForcibly();
            }
            throw new IOException("执行 uiautomator dump 时被中断。", e);
        } catch (IOException e) {
            if (dumpProcess != null) {
                dumpProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }


        // 读取 dump 的 XML 文件内容
        Process catProcess = null;
        StringBuilder xmlContent = new StringBuilder();
        try {
            catProcess = new ProcessBuilder("adb", "-s", deviceSerial, "shell", "cat", "/sdcard/window_dump.xml").start();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    xmlContent.append(line).append(System.lineSeparator());
                }
            }

            if (!catProcess.waitFor(10, TimeUnit.SECONDS)) {
                catProcess.destroyForcibly();
                throw new IOException("读取 /sdcard/window_dump.xml 超时。");
            }
            int exitCode = catProcess.exitValue();
            if (exitCode != 0) {
                // 读取错误流以获取更多信息
                StringBuilder errorOutput = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(catProcess.getErrorStream(), StandardCharsets.UTF_8))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorOutput.append(line).append(System.lineSeparator());
                    }
                }
                throw new IOException("读取 /sdcard/window_dump.xml 失败，退出码: " + exitCode + ". 错误信息: " + errorOutput.toString().trim());
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            if (catProcess != null) {
                catProcess.destroyForcibly();
            }
            throw new IOException("读取 /sdcard/window_dump.xml 时被中断。", e);
        } catch (IOException e) {
            if (catProcess != null) {
                catProcess.destroyForcibly();
            }
            throw e; // 重新抛出原始的 IOException
        }

        if (xmlContent.length() == 0) {
            log.warn("获取到的 UI 层级 XML 内容为空。设备: {}", deviceSerial);
            // 可以选择抛出异常或返回空字符串，具体取决于业务需求
            // throw new IOException("获取到的 UI 层级 XML 内容为空。");
        }

        return xmlContent.toString().trim();
    }

    @Override
    public RectSize getSize() {
        return null;
    }

    @Override
    public Frame grabFrame() {
        return new Frame();
    }
}
