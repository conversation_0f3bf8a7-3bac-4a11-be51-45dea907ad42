package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.CyclicTask;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

/**
 * <AUTHOR> yongxu.gao
 * @date : 2025/5/9 15:56
 * @Version: 1.0
 * @Desc : ZCAN_CANFDDTU400EWGR_200U底层接口
 */
public class ZlgCanFdDtu400UEwgr extends ZlgCan{
    private final ZlgCanAbstractionLayer can;

    public ZlgCanFdDtu400UEwgr(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        can = new ZlgCanAbstractionLayer();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.ZLG_CANFDDTU_400UEWGR;
    }

//    @Override
//    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration) {
//        can.sendPeriod(getChannelHandle(message.getChannel()), message);
//        return null;
//    }
//
//    @Override
//    public CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
//        ZlgApi.stopCanPeriodMessage(getChannelHandle(deviceChannel),deviceChannel,messageId);
//        return null;
//    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        boolean isSendOk;
        if (timeout != null) {
            //阻塞发送
            isSendOk = can.blockingSend(message, timeout);
        } else {
            //正常发送
            isSendOk = can.send(getChannelHandle(message.getChannel()), message);
        }

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }
}
