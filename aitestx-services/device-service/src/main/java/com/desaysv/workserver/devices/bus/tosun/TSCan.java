package com.desaysv.workserver.devices.bus.tosun;

import cantools.dbc.DbcReader;
import cantools.dbc.DecodedSignal;
import cantools.dbc.Message;
import cantools.dbc.Signal;
import cantools.exceptions.DecodingFrameLengthException;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson2.JSON;
import com.desaycv.tosuncan.exceptions.TSCanException;
import com.desaycv.tosuncan.jna.HardwareMapping;
import com.desaycv.tosuncan.model.TSCanReturnData;
import com.desaycv.tosuncan.model.TSFlexrayReturnData;
import com.desaycv.tosuncan.service.TSMasterService;
import com.desaycv.tosuncan.service.impl.CircularBuffer;
import com.desaycv.tosuncan.service.impl.TSMasterServiceImpl;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.queue.FixedSizeQueue;
import com.desaysv.workserver.canlog.service.CanLogService;
import com.desaysv.workserver.canlog.service.impl.CanLogServiceImpl;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.devices.bus.base.*;
import com.desaysv.workserver.devices.bus.base.can.CanLogRealTimeSaveParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageRealTimeSave;
import com.desaysv.workserver.devices.bus.base.can.SequenceableCanBus;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.model.BusData;
import com.desaysv.workserver.devices.bus.nican.CanMessageVo;
import com.desaysv.workserver.devices.bus.nican.CanSignalVo;
import com.desaysv.workserver.entity.ActionSequenceExecutionContextInfo;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.desaycv.tosuncan.jna.TCCANConstant.*;
import static com.desaysv.workserver.utils.StrUtils.compareStrings;

/**
 * 同星Can底层接口
 **/
@Component
@Lazy
@Slf4j
public abstract class TSCan extends SequenceableCanBus {
    @Autowired
    private ActionSequenceExecutionContextInfo context; //当前运行行数据
    private TSMasterService tsMasterService;
    private final ThreadPoolExecutor executorService;
    private CanLogService canLogService;
    private final FixedSizeQueue<CanMessageVo> fixedFrameQueue = new FixedSizeQueue<>(99999);
    private String logName = "test";
    private boolean canStarted = false;
    static final String DEFAULT_LOG_PATH = "D:\\FlyTest\\log\\Details\\client\\canlog";

    public TSCan() {
        this(new DeviceOperationParameter());
    }

    public TSCan(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        // 设置线程池属性（高负载情况下需要评估线程池的设置）
        executorService = new ThreadPoolExecutor(
                10, // core pool size
                50, // maximum pool size
                60, // keep alive time
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000), // 设置有界队列
                new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
        );
    }

    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean open() throws DeviceOpenException {
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if (canConfig != null) {
            canConfig.getConfigParameters().clear();
            updateConfig(canConfig);
        }
        tsMasterService = TSMasterServiceImpl.getInstance();
        return openDevice();
    }

    /**
     * 自动打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean autoOpen() throws DeviceOpenException {
        //改成从CanConfig获取
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if (canConfig == null) {
            throw new DeviceOpenException("同星配置文件不存在，请删除设备重新连接");
        }
        Map<String, CanConfigParameter> configParameters = canConfig.getConfigParameters();
        if (CollectionUtils.isEmpty(configParameters)) {
            throw new DeviceOpenException("同星配置文件不含通道，请删除设备重新连接");
        }
        tsMasterService = TSMasterServiceImpl.getInstance();
        boolean isOpenOk = openDevice();

        if (isOpenOk) {
            for (Map.Entry<String, CanConfigParameter> entry : configParameters.entrySet()) {
                Object openParams = entry.getValue();
                if (openParams != null) {
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(openParams), CanConfigParameter.class), canConfig);
                }
            }
           /* Object ch0OpenParams = configParameters.get("1");
            Object ch1OpenParams = configParameters.get("2");
            if (ch0OpenParams != null) {
                //打开通道1
                openChannelInternal(JSON.parseObject(JSON.toJSONString(ch0OpenParams), CanConfigParameter.class), canConfig);
            }
            if (ch1OpenParams != null) {
                //打开通道2
                openChannelInternal(JSON.parseObject(JSON.toJSONString(ch1OpenParams), CanConfigParameter.class), canConfig);
            }*/
        }
        if (!isSimulated()) {
            //自动连接
            try {
                tsMasterService.connect();
            } catch (TSCanException e) {
                throw new DeviceOpenException("调用同星connect接口失败");
            }
        }

        return isOpenOk;
    }

    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    private boolean openDevice() throws DeviceOpenException {
        if (isSimulated()) {
            return true;
        }
        if (canStarted) {
            log.info("同星设备已连接:{}", getDeviceName());
            return true;
        }
        try {
            canStarted = tsMasterService.start(getDeviceModel(), getDeviceIndex());
        } catch (TSCanException e) {
            throw new DeviceOpenException(e);
        }
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
        log.info("关闭同星CAN:{}", getDeviceName());
        boolean isOk = super.close();
        if (!isSimulated()) {
            if (tsMasterService != null) {
                try {
                    tsMasterService.stop(getDeviceModel(), getDeviceIndex());
                    canStarted = false;
                } catch (TSCanException e) {
                    throw new DeviceCloseException(e);
                }
            }
        }
        return isOk;
    }

    public int getTsChannelCountByDeviceName(String deviceName) {
        return tsMasterService.getChannelCountByDeviceName(getDeviceType(), deviceName);
    }

    @Override
    public void send(FlexrayMessage message, Float timeout) throws BusError {
        if (isSimulated()) {
            return;
        }
        if (tsMasterService != null) {
            try {
                tsMasterService.sendFlexrayData(getDeviceModel(), getDeviceIndex(), (byte) message.getChannel().intValue(),
                        message.getChannelMask().byteValue(),
                        Byte.parseByte(Integer.toHexString(message.getSlotId())),
                        Byte.parseByte(Integer.toHexString(message.getOffest())),
                        Byte.parseByte(Integer.toHexString(message.getRepetitior())),
                        message.getData());
            } catch (TSCanException e) {
                throw new BusError(e);
            }
        }
    }

    /**
     * 打开通道
     *
     * @param canConfigParameter
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        //写入配置文件
        CanConfig canConfig = writeConfig(canConfigParameter);
        if (!canStarted) {
            //未打开设备尝试打开设备
            openDevice();
        }
        return openChannelInternal(canConfigParameter, canConfig);
    }

    @Override
    public boolean openChannel(int deviceChannel) throws DeviceOpenException {
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        Map<String, CanConfigParameter> configParameters = canConfig.getConfigParameters();
        for (Map.Entry<String, CanConfigParameter> entry : configParameters.entrySet()) {
            Object openParams = entry.getValue();
            if (openParams != null && deviceChannel == Integer.parseInt(entry.getKey())) {
                return openChannelInternal(JSON.parseObject(JSON.toJSONString(openParams), CanConfigParameter.class), canConfig);
            }
        }
        return false;
    }

    /**
     * 获取最大通道数
     *
     * @return 最大通道数
     */
    @Override
    public int getMaxChannelCount() {
        return getTsChannelCountByDeviceName(getDeviceModel());
    }

    /**
     * 打开通道
     *
     * @param canConfigParameter
     * @param canConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(CanConfigParameter canConfigParameter, CanConfig canConfig) throws DeviceOpenException {
        if (!isSimulated()) {
            try {
                int channel = canConfigParameter.getChannel();
                if (channel == -1) {
                    for (Map.Entry<String, CanConfigParameter> entry : canConfig.getConfigParameters().entrySet()) {
                        startChannel(entry.getValue());
                    }
                   /* //启动通道1
                    startChannel(canConfig.getConfigParameters().get("1"));
                    //启动通道2
                    startChannel(canConfig.getConfigParameters().get("2"));*/
                } else {
                    startChannel(canConfigParameter);
                }
                tsMasterService.connect();
            } catch (TSCanException e) {
                setChannelConfigured(false);
                throw new DeviceOpenException(e);
            }
        }
        setChannelConfigured(true);
        return true;
    }

    /**
     * 启动通道
     *
     * @param canConfigParameter 通道参数
     * @return
     */
    private boolean startChannel(CanConfigParameter canConfigParameter) throws TSCanException {
        log.info("打开同星{}通道{}:\n{}",
                getDeviceName(),
                canConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(canConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        int channel = canConfigParameter.getChannel();
        boolean isNormalMode = canConfigParameter.isNormalWorkMode();//工作模式 "只听模式"/"正常模式"
        boolean isCanFd = canConfigParameter.isCanFd();
        boolean isEnableResistance = canConfigParameter.isTerminalResistanceEnabled();//终端电阻 "使能"/"禁能"
        boolean isStandardISO = canConfigParameter.isCanFdStandard();//CANFD标准
        int arbitrationBps = canConfigParameter.getArbitrationBpsValue() / 1000;
        int dataBps = canConfigParameter.getDataBpsValue() / 1000;
        log.info("\n通道:{}\n波特率:{}Kbps\n数据波特率:{}Mbps\n是否CANFD:{}\n是否标准CAN:{}\n是否启用电阻:{}\n",
                channel + 1, arbitrationBps / 1000, dataBps / 1000.0 / 1000.0, isCanFd, isStandardISO, isEnableResistance);
        if (isCanFd) {
            tsMasterService.setCanFDBaudrate(getDeviceModel(), getDeviceIndex(), channel, arbitrationBps, dataBps, isStandardISO ? CANFD_LFDTISOCAN : CANFD_LFDTNONISOCAN, isNormalMode ? CANFD_LFDMNORMAL : CANFD_LFDMACKOFF, isEnableResistance);
        } else {
            tsMasterService.setCanBaudrate(getDeviceModel(), getDeviceIndex(), channel, arbitrationBps, isNormalMode ? CANFD_LFDMNORMAL : CANFD_LFDMACKOFF, isEnableResistance);
        }
        tsMasterService.setChannelEnabled(getDeviceModel(), getDeviceIndex(), HardwareMapping.TLIBApplicationChannelType.APP_CAN, channel, true);
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        log.info("关闭同星CAN通道:{}", channel);
        if (isSimulated()) {
            return true;
        }
        if (tsMasterService != null) {
            try {
                boolean isOk;
                if (channel == -1) {
                    CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
                    for (Map.Entry<String, CanConfigParameter> entry : canConfig.getConfigParameters().entrySet()) {
                        CanConfigParameter canConfigParameter = entry.getValue();
                        tsMasterService.setChannelEnabled(getDeviceModel(), getDeviceIndex(), HardwareMapping.TLIBApplicationChannelType.APP_CAN, canConfigParameter.getChannel(), false);
                        return true;
                    }
                   /* isOk = tsMasterService.setChannelEnabled(HardwareMapping.TLIBApplicationChannelType.APP_CAN, 0, false)
                            & tsMasterService.setChannelEnabled(HardwareMapping.TLIBApplicationChannelType.APP_CAN, 1, false);
                    return isOk;*/
                }
                tsMasterService.setChannelEnabled(getDeviceModel(), getDeviceIndex(), HardwareMapping.TLIBApplicationChannelType.APP_CAN, channel, false);
            } catch (TSCanException e) {
                throw new DeviceCloseException(e);
            }
        }
        return true;
    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        if (!isSimulated() && tsMasterService != null) {
            try {
                tsMasterService.writeCanMessage(getDeviceModel(), getDeviceIndex(), message.getChannel(), message.getArbitrationId(),
                        message.getData(),
                        message.isCanFd(),
                        message.isExtendedId(),
                        message.isRemoteFrame());

            } catch (TSCanException e) {
                throw new BusError(e);
            }
        }
        if (isSimulated() && fixedFrameQueue.isEnabled()) {
            CanMessageVo canMessageVo = new CanMessageVo();
            canMessageVo.setTime(System.currentTimeMillis());
            canMessageVo.setChn(String.valueOf(message.getChannel()));
            canMessageVo.setId(message.getHexId());
            canMessageVo.setEventType(message.isCanFd() ? "CAN FD" : "CAN");
            canMessageVo.setDir("Tx");
            canMessageVo.setName("");
            canMessageVo.setDlc(message.getDlc());
            canMessageVo.setData(message.getData());
            try {
                fixedFrameQueue.add(canMessageVo);
            } catch (InterruptedException e) {
                log.error("同星CAN写入报文显示队列中断", e);
            } finally {
                //使用统一线程队列处理sse发送
                String jsonString = JSON.toJSONString(canMessageVo);
                SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER + canMessageVo.getChn(), jsonString);
            }
        }
    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }

    @Override
    public void startDbcReceiver(Integer deviceChannel, DbcConfig dbcConfig) {
        List<Message> dbcMessages = new ArrayList<>();
        for (String s : dbcConfig.getDbcPaths()) {
            DbcReader dbcReader = new DbcReader();
            dbcReader.parseFile(new File(s));
            dbcMessages.addAll(dbcReader.getBus().getMessages());
        }
        tsMasterService.readDBC(tsCanReturnData -> {
            CanMessageVo canMessageVo = fetchData(tsCanReturnData, dbcMessages, tsCanReturnData.getTimestamp());
            executorService.submit(() -> {
                //实时保存
                if (canLogService != null) {
                    canLogService.realTimeSaveData(Collections.singletonList(canMessageVo));
                }
            });
            executorService.submit(() -> {
                if (canMessageVo != null) {
                    String jsonString = JSON.toJSONString(canMessageVo);
                    SseUtils.pubMsg(SseConstants.CAN_DBC_RECEIVER + canMessageVo.getChn(), jsonString);
                }
            });
        });
    }

    @Override
    public void stopDbcReceiver(Integer deviceChannel) {
        tsMasterService.stopReadDBC();
    }

    @Override
    public void  startFrameReceiver(Integer deviceChannel) throws TSCanException {
        //TODO:是否只能存9999条，是否两个通道都读取？
        fixedFrameQueue.setEnabled(true);
        fixedFrameQueue.clear();
        //帧接收
        if (isSimulated()) {
            simulateReceiveCanFrameData(deviceChannel);
        } else {
            receiveCanFrameData();
        }
    }

    @Override
    public void stopFrameReceiver(Integer deviceChannel) throws BusError {
        try {
            tsMasterService.stopRead();
        } catch (TSCanException e) {
            throw new BusError(e);
        } finally {
            fixedFrameQueue.setEnabled(false);
        }
    }

    private void receiveCanFrameData() throws TSCanException {
        tsMasterService.read(tsCanReturnData -> {
            CanMessageVo canMessageVo = fetchData(tsCanReturnData, tsCanReturnData.getTimestamp());
            executorService.submit(() -> {
                //实时保存日志
                if (canLogService != null) {
                    canLogService.realTimeSaveData(Collections.singletonList(canMessageVo));
                }
            });
            executorService.submit(() -> {
                String jsonString = JSON.toJSONString(canMessageVo);
                SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER + canMessageVo.getChn(), jsonString);
                //帧保存日志快照
                try {
                    fixedFrameQueue.add(canMessageVo);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
        });
    }

    private void simulateReceiveCanFrameData(Integer deviceChannel) {
        for (int i = 5; i < 20; i++) {
            CanMessageVo canMessageVo = new CanMessageVo();
            canMessageVo.setChn(String.valueOf(deviceChannel));
            // 使用十进制格式设置ID，但保持0x前缀
            canMessageVo.setId(String.format("0x%02d", i));
            canMessageVo.setName("test");
            canMessageVo.setEventType("CAN");
            canMessageVo.setDir("Rx");
            canMessageVo.setDlc(8);

            // 初始化数据数组
            byte[] data = new byte[8];

            // 将十进制数转换为正确的BCD编码格式
            String decimalStr = String.format("%08d", i);  // 8位，不足前面补0

            // 从右向左，每两位十进制数字组合成一个字节
            for (int j = 0; j < 4; j++) {
                int pos = decimalStr.length() - (j + 1) * 2;
                if (pos < 0) break;

                // 取两位数字
                String twoDigits = decimalStr.substring(pos, pos + 2);
                // 转换为BCD编码（十六进制编码的十进制数）
                int tens = Character.digit(twoDigits.charAt(0), 10);
                int ones = Character.digit(twoDigits.charAt(1), 10);
                // 将十位数字左移4位，再与个位数字合并
                data[7 - j] = (byte) ((tens << 4) | ones);
            }

//            log.info(ByteUtils.bytesToHexString(data));
            canMessageVo.setData(data);
            canMessageVo.setTime(System.currentTimeMillis());
            try {
                fixedFrameQueue.add(canMessageVo);
            } catch (InterruptedException e) {
                log.error("添加模拟数据失败", e);
            }
            String jsonString = JSON.toJSONString(canMessageVo);
            SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER + canMessageVo.getChn(), jsonString);
        }
    }


    private CanMessageVo fetchData(TSCanReturnData tsCanReturnData, List<Message> dbcMessages, long elapsedTimeNanos) {
        //log.info("读取同星CAN报文");
        double elapsedTimeSeconds = Double.parseDouble(String.format("%.6f", elapsedTimeNanos / 1_000_000.0));
        String id = String.format("0x%02X", tsCanReturnData.getId());
        Message message = dbcMessages.stream().filter(o -> o.getId().equals(id)).findFirst().orElse(null);
        if (message != null) {
            try {
                CanMessageVo canMessageVo = new CanMessageVo();
                canMessageVo.setTime(elapsedTimeSeconds);
                canMessageVo.setId(id);
                canMessageVo.setChn(String.valueOf(tsCanReturnData.getChannel() + 1));
                canMessageVo.setEventType(tsCanReturnData.getEventType());
                canMessageVo.setDir(tsCanReturnData.getDir());
                canMessageVo.setDlc(tsCanReturnData.getDlc());
                //这里如果出现长度不一致的情况直接截断或者补00
                byte[] validPayload = Arrays.copyOf(tsCanReturnData.getData(), message.getLength());
                canMessageVo.setData(validPayload);
                List<CanSignalVo> canSignalVoList = new ArrayList<>();
                List<Signal> signals = message.getSignals();
                Map<String, DecodedSignal> decodeData = message.decodeByDecodedSignal(validPayload);
                for (Signal signal : signals) {
                    Map.Entry<String, DecodedSignal> stringDecodedSignalEntry = decodeData.entrySet().stream().filter(o -> o.getKey().equals(signal.getName())).findFirst().get();
                    DecodedSignal decodedSignal = stringDecodedSignalEntry.getValue();
                    CanSignalVo canSignalVoR = new CanSignalVo(signal.getName(), String.valueOf(decodedSignal.getRawValue()), decodedSignal.getPhyValue(), signal.getNotes());
                    canSignalVoList.add(canSignalVoR);
                }
                canMessageVo.setName(message.getName());
                canMessageVo.setCanSignalList(canSignalVoList);
                return canMessageVo;
            } catch (DecodingFrameLengthException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }

    private CanMessageVo fetchData(TSCanReturnData tsCanReturnData, long elapsedTimeNanos) {
        //log.info("读取同星CAN报文");
        double elapsedTimeSeconds = Double.parseDouble(String.format("%.6f", elapsedTimeNanos / 1_000_000.0));
        String id = String.format("0x%02X", tsCanReturnData.getId());
        CanMessageVo canMessageVo = new CanMessageVo();
        canMessageVo.setTime(elapsedTimeSeconds);
        canMessageVo.setId(id);
        canMessageVo.setChn(String.valueOf(tsCanReturnData.getChannel() + 1));
        canMessageVo.setEventType(tsCanReturnData.getEventType());
        canMessageVo.setDir(tsCanReturnData.getDir());
        canMessageVo.setDlc(tsCanReturnData.getDlc());
        canMessageVo.setName("");
        byte[] validPayload = Arrays.copyOf(tsCanReturnData.getData(), tsCanReturnData.getData().length);
        canMessageVo.setData(validPayload);
        return canMessageVo;
    }

    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError {
        if (tsMasterService == null) {
            return false;
        }
        return super.setCanPTS(deviceChannel, ecuNodeName, sendMessageId, byteInstruction, recvMessageId, isChecked);
    }

    @Override
    public String verifyCanMessage(Integer deviceChannel, String messageId,  String byteData,Integer count) throws BusError {
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        String data = "";
        int judg = 0;
        for (int i = 0; i < circularBuffer.size(); i++) {
            TSCanReturnData tsCanReturnData= circularBuffer.getFromTail(i);
            if(tsCanReturnData.getChannel() == deviceChannel-1 && tsCanReturnData.getId() == Integer.parseInt(messageId.replace("0x",""), 16)){
                data = ByteUtils.byteArrayToHexString(tsCanReturnData.getData());
                if(compareStrings(byteData, data, 'X')){
                    return data;
                }else{
                    judg++;
                }
            }
            if(judg>=count){
                break;
            }
        }
        return data;
    }

    @Override
    public long getLatestCanTimestamp(Integer deviceChannel) {
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        if (!circularBuffer.isEmpty()) {
            return circularBuffer.getFromTail(0).getTimestamp();
        }
        return 0;
    }

    @Override
    public Object getReceiveBuffer() {
        return tsMasterService.getCircularBuffer();
    }

    @Override
    public Object readFromTail(Object buffer, int index) {
        return ((CircularBuffer<TSCanReturnData>) buffer).getFromTail(index);
    }

    @Override
    public int getDataFrameId(Object data) {
        return ((TSCanReturnData) data).getId();
    }

    @Override
    public long getTimestamp(Object data) {
        return ((TSCanReturnData) data).getTimestamp();
    }

    @Override
    public byte[] getDataBytes(Object data) {
        return ((TSCanReturnData) data).getData();
    }

    @Override
    public boolean isBufferEmpty(Object buffer) {
        return ((CircularBuffer<TSCanReturnData>) buffer).isEmpty();
    }

    @Override
    public int getBufferSize(Object buffer) {
        return ((CircularBuffer<TSCanReturnData>) buffer).size();
    }
    @Override
    public void sendFlowControlFrame(CanMessage canMessage, byte[] flowControlFrame) throws BusError {
        canMessage.setData(flowControlFrame);
        canMessage.setSendTimes(1);
        send(canMessage);
    }

    @Override
    public boolean isValidFirstFrame(Object data) {
        return getDataBytes(data)[0] == 0x10;
    }

    @Override
    public boolean isValidFlowControlFrame(Object data, int receiveId, Object msgData) {
        return getDataBytes(data)[0] == 0x30 &&
                getDataFrameId(data) == receiveId &&
                getTimestamp(data) > getTimestamp(msgData);
    }

    @Override
    public boolean isContinueFrame(Object data) {
        return getDataBytes(data)[0] == 0x31;
    }

    @Override
    public boolean isAbortFrame(Object data) {
        return getDataBytes(data)[0] == 0x32;
    }

    @Override
    public String checkReplyData(Integer deviceChannel, String messageId, String byteData) {
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        byte[] expectedData = buildExpectedData(byteData);
        return checkReplyDataCommonLogic(deviceChannel, messageId, expectedData, circularBuffer);
    }

    @Override
    public String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError{
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        byte[] expectedData = buildExpectedData(expectResult);
        return checkReplyDataCommonLogic(deviceChannel, "", expectedData, circularBuffer);
    }

    private byte[] buildExpectedData(String byteData) {
        byte[] expectedData = ByteUtils.hexStringToByteArray(byteData);
        byte[] newData = new byte[expectedData.length + 1];
        newData[0] = (byte) expectedData.length;
        System.arraycopy(expectedData, 0, newData, 1, expectedData.length);
        return newData;
    }

    @Override
    public int getChannelFromData(Object data) {
        if (data instanceof TSCanReturnData) {
            return ((TSCanReturnData) data).getChannel();
        }
        throw new IllegalArgumentException("Unsupported data type: " + data.getClass());
    }


    @Override
    public byte[] readDataByIdHex(int deviceChannel, int targetCanId, boolean isCanFd, long timeoutMilliseconds) throws TSCanException {
        long timeout = isSimulated() ? 0 : timeoutMilliseconds;
        TSCanReturnData tsCanReturnData = tsMasterService.readDataByIdHex(getDeviceModel(), getDeviceIndex(), deviceChannel, Integer.toHexString(targetCanId), timeout, isCanFd);
        return tsCanReturnData == null ? new byte[]{} : tsCanReturnData.getData();
    }

    @Override
    public String fetchCanPTS(Integer deviceChannel, String messageId) throws BusError {
        if (tsMasterService == null) {
            return "";
        }
        return super.fetchCanPTS(deviceChannel, messageId);
    }

    @Override
    public boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        try {
            String result = fetchCanPTS(deviceChannel, messageId);
            // 有效性判断逻辑
            boolean existMsg = !("NA".equals(result) || result.isEmpty());
            return exist == existMsg;
        } catch (BusError e) {
            log.error("CAN通道{}检查报文ID异常：{}", deviceChannel, e.getMessage());
            return false;
        }
    }

    @Override
    public String notificationUpgrade(int fileType) {
        return null;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        return false;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) {
        return 0;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) {
        return false;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
//        String baseDirPath = "D:" + File.separator + "FlyTest" + File.separator + "log" + File.separator + "Details" + File.separator + "client" + File.separator + "canlog";
//        logName = baseDirPath + File.separator + canLogName.replace("_", "") + ".blf";
//
//        File baseDir = new File(baseDirPath);
//        if (!baseDir.exists()) {
//            boolean created = baseDir.mkdirs();
//            if (created) {
//                log.info("成功创建CAN日志目录: {}", baseDir.getAbsolutePath());
//            } else {
//                log.error("无法创建CAN日志目录: {}", baseDir.getAbsolutePath());
//                return false;
//            }
//        }
        return true;
    }

    private boolean createLogPath() {
        final String tcID = context.getTcId();
        final String tableName = context.getTableName();
        final String customPath = context.getUserLogPath();

        final String finalLogPath = (customPath != null && tcID != null && tableName != null)
                ? customPath + File.separator + tableName + File.separator + tcID
                : DEFAULT_LOG_PATH;

        try {

            Path dirPath = Paths.get(finalLogPath);
            Files.createDirectories(dirPath);
            log.info("成功创建CAN日志目录: {}", dirPath);

            String fileName = (tcID != null ? tcID : "unknown") + ".blf";
            Path filePath = dirPath.resolve(fileName);

            logName = filePath.toString();
            log.info("CAN日志文件: {}", logName);
            return true;
        } catch (IOException e) {
            log.error("创建日志目录失败: {}", e.getMessage());

            try {
                Path defaultPath = Paths.get(DEFAULT_LOG_PATH);
                Files.createDirectories(defaultPath);
                logName = defaultPath.resolve("default.blf").toString();
                log.warn("已使用默认日志路径: {}", logName);
                return true;
            } catch (IOException ex) {
                log.error("创建默认日志目录失败: {}", ex.getMessage());
                return false;
            }
        }
    }


    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        try {
            if (commandId == 0) {
                if (createLogPath()) {
                    if (StringUtils.isEmpty(logName)) return false;
                    startFrameReceiver(deviceChannel);
                    startRealTimeData(deviceChannel, new CanMessageRealTimeSave(logName));
                }
            } else if (commandId == 1) {
                stopFrameReceiver(deviceChannel);
//                saveLog(new CanMessageRealTimeSave(logName));
                stopRealTimeData();
            } else {
                log.error("未知的同星CAN日志命令:{}", commandId);
                return false;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return true;
    }

    @Override
    public void startRealTimeData(Integer deviceChannel, CanMessageRealTimeSave canMessageRealTimeSave) {
        log.info("开启同星通道:{} CAN日志实时保存", deviceChannel);
        canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.startRealTimeData();
    }

    @Override
    public void stopRealTimeData() {
        log.info("关闭同星CAN日志实时保存");
        canLogService.stopRealTimeData();
        canLogService = null;
    }

    @Override
    public void startCaptureFrameCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter){
        log.info("添加同星{}通道{}日志(Frame)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}",  canLogRealTimeSaveParameter.getFriendlyString());
        try {
            startFrameReceiver(deviceChannel);
        }catch (Exception e){
            log.error("开启读取同星{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureFrameCanLog(Integer deviceChannel){
        log.info("停止同星{}日志(Frame)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopFrameReceiver(deviceChannel);
        } catch (Exception e){
            log.error("关闭读取同星{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }

    @Override
    public void startCaptureDbcCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter){
        log.info("添加同星{}通道{}日志(DBC)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}",  canLogRealTimeSaveParameter.getFriendlyString());
        startDbcReceiver(deviceChannel,  canLogRealTimeSaveParameter.getDbcConfig());
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureDbcCanLog(Integer deviceChannel){
        log.info("停止同星{}日志(DBC)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopDbcReceiver(deviceChannel);
        } catch (Exception e){
            log.error("关闭读取同星{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }


    @Override
    public void saveLog(CanMessageRealTimeSave canMessageRealTimeSave) throws BlfException {
        log.info("保存同星CAN日志:{}", canMessageRealTimeSave.getFilePath());
        CanLogServiceImpl canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.saveLog(fixedFrameQueue);
    }

    public byte[] readFlexrayDataByIdCycle(Integer deviceChannel, FlexrayMessage message) throws OperationFailNotification, TSCanException {
        long timeout = isSimulated() ? 0 : TIMEOUT_MILLISECONDS;
        TSFlexrayReturnData tsFlexrayReturnData = tsMasterService.readFlexrayDataByIdCycle(getDeviceModel(), getDeviceIndex(), deviceChannel, Short.parseShort(Integer.toHexString(message.getSlotId())), (short) message.getOffest().intValue(), (short) message.getRepetitior().intValue(), timeout);
        if (tsFlexrayReturnData == null) {
            String str = String.format("读取flexray报文失败，未读取到slotId为%d，offset为%d，rep为%d的Flexray报文", Short.parseShort(Integer.toHexString(message.getSlotId())), (short) message.getOffest().intValue(), (short) message.getRepetitior().intValue());
            log.info(str);
            throw new OperationFailNotification(str);
        }
        return tsFlexrayReturnData.getData();
    }


    public CyclicTask stopFlexrayMessage(Integer deviceChannel, String messageStr) throws TSCanException {
        String[] message = messageStr.split("-");
        log.info("{}停止Flexray通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%s", message[0]));
        return stopFlexrayMessageInternal(deviceChannel, Integer.parseInt(message[0]), Integer.parseInt(message[1]), Integer.parseInt(message[2]));
    }

    private CyclicTask stopFlexrayMessageInternal(int channel, int slotId, int base, int rep) throws TSCanException {
        CyclicTask task = getCyclicTaskByTaskId(TaskIdGenerator.generateTaskId(channel, slotId, base, rep));
        if (task != null) {
            task.stop();
        }
        //删除本地Task的同时再调用接口删除cycle
        tsMasterService.stopSendFlexrayData(getDeviceModel(), getDeviceIndex(), (byte) channel, Byte.parseByte(Integer.toHexString(slotId)), Byte.parseByte(Integer.toHexString(base)), Byte.parseByte(Integer.toHexString(rep)));
        return task;
    }

    @Override
    public void send(FlexrayMessage message) throws BusError {
        Float timeout = null;
        send(message, timeout);
    }

    @Override
    public void stopAllFlexrayMessage(Integer deviceChannel) throws TSCanException {
        stopAllFlexrayPeriodicTasks(deviceChannel);
    }

    @Override
    public void stopAllFlexrayPeriodicTasks(Integer deviceChannel) throws TSCanException {
        stopAllFlexrayPeriodicTasks(deviceChannel, true);
    }

    @Override
    public void stopAllFlexrayPeriodicTasks(Integer deviceChannel, boolean removeTasks) throws TSCanException {
        if (!taskRunning()) {
            return;
        }
        if (deviceChannel == null) {
            log.info("{}停止通道所有Flexray报文", getDeviceName());
        } else {
            log.info("{}停止通道{}所有Flexray报文", getDeviceName(), deviceChannel);
        }
        List<CyclicTask> tasks = new ArrayList<>(getPeriodTasks());
        for (CyclicTask task : tasks) {
            if (deviceChannel == null || task.getTaskId().getChannel() == deviceChannel) {
                task.stop(removeTasks);
                FlexrayMessage flexrayMessage = ((ThreadBasedCyclicSendTask) task).getFlexrayMessage();
                if (flexrayMessage != null) {
                    tsMasterService.stopSendFlexrayData(getDeviceModel(), getDeviceIndex(), (byte) flexrayMessage.getChannel().intValue(), Byte.parseByte(Integer.toHexString(flexrayMessage.getSlotId())), Byte.parseByte(Integer.toHexString(flexrayMessage.getOffest())), Byte.parseByte(Integer.toHexString(flexrayMessage.getRepetitior())));
                }
            }
        }
    }

    public void startTsMaster() throws TSCanException {
        log.info("连接TsMaster");
        tsMasterService.start(getDeviceModel(), getDeviceIndex());
        tsMasterService.connect();
        canStarted = true;
    }

    public void stopTsMaster() throws TSCanException {
        log.info("断开TsMaster");
        stopAllFlexrayMessage(null);
        tsMasterService.stop(getDeviceModel(), getDeviceIndex());
        canStarted = false;
    }

    @Override
    public void clearBuffer(int deviceChannel) {
        try {
            tsMasterService.clearBuffer(getDeviceModel(), getDeviceIndex(), deviceChannel);
        } catch (TSCanException e) {
            log.error(e.getMessage(), e);
        }
    }


    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        BusData busData = getTosunBusDataByJsonConfig(deviceChannel);
        return sendAllIGModuleMessages(busData, deviceChannel, command);
    }

    @Override
    public double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError {
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        List<Long> list = new ArrayList<>();
        for (int i = 0; i < circularBuffer.size(); i++) {
            TSCanReturnData tsCanReturnData= circularBuffer.getFromTail(i);
            if(tsCanReturnData.getChannel() == deviceChannel-1 && tsCanReturnData.getId() == Integer.parseInt(messageId.replace("0x",""), 16)){
                log.info("{}获取报文ID:{}时间戳:{}", getDeviceName(), messageId, tsCanReturnData.getTimestamp());
                list.add(tsCanReturnData.getTimestamp());
            }
            if(list.size() == 2){
                break;
            }
        }
        if (list.size() == 2) {
            long cycleTimeStamp = list.get(0) - list.get(1);
            return Double.parseDouble(String.format("%.3f", cycleTimeStamp / 1_000.0));
        }
        return -1;
    }

    @Override
    public int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError {
        CircularBuffer<TSCanReturnData> circularBuffer = tsMasterService.getCircularBuffer();
        for (int i = 0; i < circularBuffer.size(); i++) {
            TSCanReturnData tsCanReturnData= circularBuffer.getFromTail(i);
            if(tsCanReturnData.getChannel() == deviceChannel-1 && tsCanReturnData.getId() == Integer.parseInt(messageId.replace("0x",""), 16)){
                return tsCanReturnData.getDlc();
            }
        }
        return -1;
    }


    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        BusData busData = getTosunBusDataByJsonConfig(deviceChannel);
        return sendSingleIGModuleMessages(busData, deviceChannel, igTabName, command);
    }

    private BusData getTosunBusDataByJsonConfig(Integer deviceChannel) {
        String filePath = String.format("D:\\FlyTest\\data\\client\\projects\\%s\\config\\dbc\\%s\\channel%d\\data.json", projectName, getDeviceName(), deviceChannel);
        return importBusDataFile(filePath);
    }

    public static void main(String[] args) {
//        TSMasterService tsMasterService = new TSMasterServiceImpl();
//        String workingDir = System.getProperty("user.dir");
//        System.out.println("当前工作目录: " + workingDir);
//        try {
//            if (tsMasterService.start()) {
//                System.out.println("获取session成功");
//                tsMasterService.writeCanMessage(0, 301, new byte[]{0x00, 0x08, 0x00, 0x00}, false, false, false);
//            }
//        } catch (TSCanException e) {
//            throw new RuntimeException(e);
//        }
//        String byteInstruction = "037F221300000";
//        String s = ByteUtils.byteArrayToHexString(new byte[]{0X03, 0x7F, 0x22, 0x13, 0x00, 0x00, 0x00, 0x00});
//        System.out.println("PTS---" + s);
//        boolean x = compareStrings(byteInstruction, s, 'X');
//        System.out.println("sss---" + s);
//        System.out.println(StrUtils.containsIgnoreCase("NA", BaseRegexRule.PTX_RX_CONSTANT));

        List<Long> list = new ArrayList<>();
        list.add(271397772L);
        list.add(270397783L);
        list.add(271397467L);
        list.add(270397479L);
        list.add(271398080L);
        list.add(270398091L);
        long cycleTimeStamp = list.get(0) - list.get(1);
        long cycleTimeStamp2 = list.get(2) - list.get(3);
        long cycleTimeStamp3 = list.get(4) - list.get(5);
        double cycleTime = Double.parseDouble(String.format("%.3f", cycleTimeStamp / 1_000.0));
        double cycleTime2 = Double.parseDouble(String.format("%.3f", cycleTimeStamp2 / 1_000.0));
        double cycleTime3 = Double.parseDouble(String.format("%.3f", cycleTimeStamp3 / 1_000.0));
        System.out.println(cycleTime);
        System.out.println(cycleTime2);
        System.out.println(cycleTime3);
        boolean pass = Math.abs(cycleTime - 1000) <= 10;
        System.out.println( pass);
    }

}