package com.desaysv.workserver.devices.camera.base;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.common.port.PortDevice;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.camera.config.CameraConfig;
import com.desaysv.workserver.entity.VisionResult;
import com.desaysv.workserver.exceptions.OperationParameterExtractException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.command.ImageOperationCommand;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.stream.GrabRequest;
import com.desaysv.workserver.stream.StreamService;
import com.desaysv.workserver.utils.ExceptionUtils;
import com.desaysv.workserver.utils.SpringContextHolder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestTemplate;

/**
 * 相机设备
 */
@Slf4j
public abstract class CameraDevice extends PortDevice<CameraConfig> implements IVisionDevice {

    @Getter
    private final String deviceType = DeviceType.DEVICE_CAMERA;

    @JSONField(serialize = false)
    private final RestTemplate restTemplateClient = new RestTemplate();

    public CameraDevice() {
        this(new DeviceOperationParameter());
    }

    public CameraDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    @Override
    public synchronized boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        GrabRequest grabRequest = new GrabRequest();
        grabRequest.setDeviceUUID(getDeviceUniqueCode());
        grabRequest.setDeviceName(getDeviceName());
        grabRequest.setDeviceModel(getDeviceModel());
        grabRequest.setDevicePort(getDevicePort());
        grabRequest.setSimulated(isSimulated());
        DeviceOperationParameter operationParameter = getDeviceOperationParameter();
        try {
            grabRequest.setWidth(operationParameter.getWidth());
            grabRequest.setHeight(operationParameter.getHeight());
        } catch (OperationParameterExtractException e) {
            log.warn("获取相机分辨率参数失败:{}", e.getMessage());
        }
        StreamService streamService = (StreamService) SpringContextHolder.getBean(StrUtil.lowerFirst(StreamService.class.getSimpleName()));
        if (streamService.isDistributed(getDeviceUniqueCode())) {
            streamService.outputDynamicFrame(getDeviceUniqueCode(), true);
        } else {
            try {
                String url = streamService.pushStream(grabRequest);
            } catch (Exception e) {
                log.warn(e.getMessage(), e);
                if (grabRequest.getDeviceModel().equalsIgnoreCase(DeviceModel.Camera.HIK_CAMERA)) {
                    throw new DeviceOpenException(String.format("海康相机\"%s\"推流失败，" +
                            "请检查MVS上位机是否为4.4.0以上版本，安装路径:\\\\hzhe003a\\DFS\\DIDA3019\\Div IC\\ST\\02_PTV\\07_小组管理\\03_AT\\01_自动化测试系统\\06-FlyTest\\01-软件版本\\05-硬件驱动", getDeviceName()));
                } else {
                    throw new DeviceOpenException(String.format("USB相机\"%s\"推流失败", getDeviceName()));
                }
            }
        }
//        ParameterizedTypeReference<ResultEntity<Object>> typeRef = new ParameterizedTypeReference<ResultEntity<Object>>() {
//        };
//        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/grab", NetworkUtils.getServerPort());
//        ResultEntity<Object> entity = restTemplateClient.exchange(url, HttpMethod.POST, new HttpEntity<>(grabRequest), typeRef).getBody();
//        if (entity == null) {
//            throw new DeviceOpenException(String.format("Post请求失败:%s", url));
//        }
//        if (!entity.isOk()) {
//            throw new DeviceOpenException(entity.getMessage());
//        }
//        return entity.isOk();
        return true;
    }

    @Override
    public synchronized boolean close() throws DeviceCloseException {
//        GrabRequest grabRequest = new GrabRequest();
//        grabRequest.setDeviceUUID(getDeviceUniqueCode());
//        grabRequest.setDeviceModel(getDeviceModel());
//        grabRequest.setDevicePort(getDevicePort());
//        grabRequest.setSimulated(isSimulated());
//        String url = String.format("http://127.0.0.1:%d/AITestX/device/stream/stopGrab", NetworkUtils.getServerPort());
//        return restTemplateClient.postForEntity(url, grabRequest, String.class).getStatusCode() == HttpStatus.OK;
        StreamService streamService = (StreamService) SpringContextHolder.getBean(StrUtil.lowerFirst(StreamService.class.getSimpleName()));
        //断开设备关闭录像
//        CameraConfig cameraConfig = loadConfig(getDeviceOperationParameter().getProject());
//        cameraConfig.getFailVideoConfig().setFailVideo(false);
//        updateConfig(cameraConfig);
        return streamService.stopStream(getDeviceUniqueCode());
    }

    @Override
    public VisionResult imageModelMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent, String model) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setAlgorithm(algorithm);
        visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        visionRecognizeRequest.setRoiEnlargePercent(roiEnlargePercent);
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getModelVisionResult(this, visionRecognizeRequest, model);
            return imageOperationCommand.handleModelVisionResult(visionResult, visionRecognizeRequest.getProject(), this);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }

    @Override
    public VisionResult imageMatch(String templateName, boolean mustExist, float threshold, String matchText, String algorithm, float roiEnlargePercent,
                                   float preWaitTime,
                                   float continuousCheckTimeout) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setAlgorithm(algorithm);
        //将实际字符替换成换行符号
        if (matchText != null) {
            matchText = matchText.replace("\\n", "\n");
        }
        visionRecognizeRequest.setTimeout(continuousCheckTimeout);
        visionRecognizeRequest.setPreWaitTime(preWaitTime);
        visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        visionRecognizeRequest.setRoiEnlargePercent(roiEnlargePercent);
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getVisionResult(this, visionRecognizeRequest);
            return imageOperationCommand.handleVisionResult(visionResult, visionRecognizeRequest.getProject(), this, false);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }

    @Override
    public VisionResult videoMatch(String templateName, double recognizedDuration, float threshold, float targetSimilarity, String algorithm) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        VisionRecognizeRequest visionRecognizeRequest = new VisionRecognizeRequest();
        visionRecognizeRequest.setTemplateName(templateName);
        visionRecognizeRequest.setThreshold(threshold);
        visionRecognizeRequest.setTargetSimilarity(targetSimilarity);
        visionRecognizeRequest.setAlgorithm(algorithm == null ? "LightBlinking" : algorithm);
        //visionRecognizeRequest.setMatchedText(matchText);
        visionRecognizeRequest.setRecognizedDuration(recognizedDuration);
        //visionRecognizeRequest.setMustExist(mustExist);
        visionRecognizeRequest.setColorMatchEnabled(true);
        visionRecognizeRequest.setVideoEnabled(true);
        visionRecognizeRequest.setProject(getDeviceOperationParameter().getProject());
        VisionResult visionResult;
        try {
            visionResult = imageOperationCommand.getVisionResult(this, visionRecognizeRequest);
            return visionResult;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            visionResult = new VisionResult();
            visionResult.setTemplateName(templateName);
            visionResult.setMessage(ExceptionUtils.getExceptionString(e));
            return visionResult;
        }
    }

    @Override
    public OperationResult takePhoto() {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        ImageOperationCommand.ImageOperationContext imageOperationContext = new ImageOperationCommand.ImageOperationContext();
        imageOperationContext.setDevice(this);
        imageOperationContext.setOperationResult(new OperationResult());
        imageOperationContext.setProjectName(getDeviceOperationParameter().getProject());
        return imageOperationCommand.cameraScreenShoot(imageOperationContext);
    }

    @Override
    public OperationResult videoRecording(String status) {
        ImageOperationCommand imageOperationCommand = (ImageOperationCommand) SpringContextHolder.getBean(StrUtil.lowerFirst(ImageOperationCommand.class.getSimpleName()));
        ImageOperationCommand.ImageOperationContext imageOperationContext = new ImageOperationCommand.ImageOperationContext();
        imageOperationContext.setDevice(this);
        imageOperationContext.setOperationResult(new OperationResult());
        imageOperationContext.setProjectName(getDeviceOperationParameter().getProject());
        if (status.equalsIgnoreCase("start")) {
            return imageOperationCommand.startRecording(imageOperationContext);
        } else if (status.equalsIgnoreCase("stop")) {
            return imageOperationCommand.stopRecording(imageOperationContext);
        } else {
            return new OperationResult().fail("录像操作失败");
        }
    }


    @Override
    public Class<CameraConfig> getDeviceConfigClass() {
        return CameraConfig.class;
    }

    @Override
    public void updateConfig(CameraConfig deviceConfig) {
        super.updateConfig(deviceConfig);
    }
}
