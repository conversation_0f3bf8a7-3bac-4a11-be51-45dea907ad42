package com.desaysv.workserver.devices.bus.canoe.com;

import cantools.dbc.DecodedSignal;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.UdsModel;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.BaseCanDevice;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.canoe.VectorUtils;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequence;
import com.desaysv.workserver.entity.NotificationManager;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.jacob.com.Dispatch;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.Map;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.*;
import static com.desaysv.workserver.utils.StrUtils.hexStrToInt;

/**
 * Vector Canoe底层控制
 */
@Slf4j
public class VectorCan extends BaseCanDevice implements ICanSequence {
    private Dispatch namespaces;
//    private static final int NOT_RESPONSE = 255;
//    private static final int GET_RETURN_VALUE_TIMEOUT = 2000; //获取returnValue超时时间
//    private static final int PUT_RETURN_VALUE_TIMEOUT = 1000; //设置returnValue超时时间
//    private static final int GET_VARIANT_TIMEOUT = 1000; //获取信号超时时间

    public VectorCan() {

    }

    public VectorCan(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        VectorShareComponent canApp = VectorShareComponent.getInstance();
        canApp.setClose(false);
        namespaces = VectorShareComponent.namespaces;
//        canApp = new ActiveXComponent("CANoe.Application");        // 创建 CANoe Application 对象
//        Dispatch system = Dispatch.call(canApp, "System").toDispatch();  // 获取 CAPL 模块对象
//        namespaces = Dispatch.call(system, "Namespaces").toDispatch();
    }

    @Override
    public int getCanMessageCycle(Integer deviceChannel, String messageName) {
        return -1;
    }

    @Override
    public double fetchCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) {
        return fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
    }

    @Override
    public double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgName", messageName);        // 设置Can报文名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolGetSigName", signalName);        // 设置获取Can信号名称
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return -1;
        }
        putCounter(ILcontrolVariables, "ILcontrolGetSigNameCounter");    //设置获取Can信号名称counter
        if (getResult(netWorksNodesVariables)) {
            return fetchLatestSystemVariant(ILcontrolVariables, "ILcontrolGetSigValue", 2000);
        }
        return -1;
    }

    @Override
    public boolean setCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long signalRawValue) throws BusError {
        return false;
    }

    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) {
        long sTime = System.currentTimeMillis();
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);    // 设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgName", messageName);        // 设置Can报文名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolSigName", signalName);        // 设置Can信号名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolSigValue", signalPhyValue);      //设置Can信号值
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolVariables, "ILcontrolSigNameCounter");  //设置Can信号名称counter
        boolean result = getResult(netWorksNodesVariables);
        log.info("设置Can信号{}的信号值:{}, 执行时长:{}毫秒, 返回结果:{}", signalName, signalPhyValue, System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return result;
    }

    @Override
    public boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue, Integer cycle) throws BusError {
        return false;
    }

    @Override
    public void executeCycleStepLogic(String taskId, Integer deviceChannel, String ecuNodeName,
                                      String messageName, String signalName, Integer start,
                                      Integer end, int step, String interval) {
    }

    @Override
    public boolean setCanMessageCSRolling(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);     //设置ECU节点名称*/
        Dispatch csRollingVariables = VectorUtils.getVariantDispatch(namespaces, "csRolling", "Variables");
        VectorUtils.putVariant(csRollingVariables, "csRollingMsgID", hexStrToInt(messageNameOrID));             //设置报文ID
        VectorUtils.putVariant(csRollingVariables, "checksumStatus", checksumStatus);        //设置报文checksumStatus
        VectorUtils.putVariant(csRollingVariables, "rollingCounterStatus", rollingCounterStatus);  //设置报文rollingCounterStatus
        VectorUtils.putVariant(netWorksNodesVariables, "returnValue", NOT_RESPONSE);          //设置returnValue为255
        int rollingCounter = VectorUtils.getVariant(csRollingVariables, "ChecksumRollingCounter", "Value").getInt();
        VectorUtils.putVariant(csRollingVariables, "ChecksumRollingCounter", VectorUtils.randomInt(rollingCounter));   //设置报文RollingCounter
        return getResult(netWorksNodesVariables);
    }

    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) {
        // 1.CANoeCAN-ECU-PTS-70260304FFFFFFFF
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        Dispatch ptsControlVariables = VectorUtils.getVariantDispatch(namespaces, "PtsControl", "Variables");
        byte[] byteArray = ByteUtils.hexStringToByteArray(byteInstruction);
        VectorUtils.putVariant(ptsControlVariables, "PtsControlDateTx", byteArray);     //设置ECU节点名称
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ptsControlVariables, "PtsControlCounter");
        return getResult(netWorksNodesVariables);
    }

    @Override
    public boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError {
        return false;
    }


    @Override
    public boolean setCanSingleMsgStatus(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);     //设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgName", messageNameOrId);        // 设置Can报文名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgStatus", messageStatus);    // 设置Can单个通道报文状态
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolVariables, "ILcontrolMsgNameCounter");  // 设置SingleMsgNameCounter
        return getResult(netWorksNodesVariables);
    }

    @Override
    public boolean setCanEcuAllMsgStatus(Integer deviceChannel, String ecuNodeName, int messageStatus) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);     //设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolAllMsgStatus", messageStatus);       // 设置Can所有通道报文状态
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolVariables, "ILcontrolAllMsgStatusCounter");// 设置AllMsgStatusCounter
        return getResult(netWorksNodesVariables);
    }

    @Override
    public boolean setCanMessageDLC(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);     //设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgName", messageNameOrId);       // 设置Can报文名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgDLC", dlc);   // 设置Can报文周期时间
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolVariables, "ILcontrolMsgDLCCounter");//设置AllMsgStatusCounter
        return getResult(netWorksNodesVariables);
    }

    @Override
    public boolean setCanMessageCycleTime(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) {
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "NameNodes", ecuNodeName);     //设置ECU节点名称
        Dispatch ILcontrolVariables = VectorUtils.getVariantDispatch(namespaces, "ILcontrol", "Variables");
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgName", messageNameOrId);       // 设置Can报文名称
        VectorUtils.putVariant(ILcontrolVariables, "ILcontrolMsgCycTime", cycleTime);   // 设置Can报文周期时间
        if (!putReturnValue(netWorksNodesVariables, "returnValue")) {
            log.info("设置returnValue=255超时失败, 不去响应Counter, 脚本执行结束！");
            return false;
        }
        putCounter(ILcontrolVariables, "ILcontrolMsgCycTimeCounter");//设置AllMsgCycleTimeCounter
        return getResult(netWorksNodesVariables);
    }


    @Override
    public String fetchCanPTS(Integer deviceChannel, String messageId) {
        Dispatch ptsControlVariables = VectorUtils.getVariantDispatch(namespaces, "PtsControl", "Variables");
        return fetchLatestSystemVariantString(ptsControlVariables, "PtsControlDateRx", null, GET_VARIANT_TIMEOUT);
    }

    @Override
    public boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError {
        return false;
    }

    @Override
    public boolean lastCheckCanMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError {
        return false;
    }

    @Override
    public String notificationUpgrade(int fileType) {
        long sTime = System.currentTimeMillis();
        boolean result = true;
//        Dispatch pathVariables = getVariantDispatch(namespaces, "path", "Variables");
//        putVariant(pathVariables, "APP",  NotificationManager.getInstance().getFilePath()+"App_Fill_Edit_GWM.s19");
//        putVariant(pathVariables, "Flash",  NotificationManager.getInstance().getFilePath()+"FlashDriver.s19");
//        putVariant(pathVariables, "FilePath",  NotificationManager.getInstance().getFilePath());
        Dispatch upgradeVariables = VectorUtils.getVariantDispatch(namespaces, "upgrade", "Variables");
        putCounter(upgradeVariables, "upgradeCounter"); //设置upgradeCounter触发升级
        String ptsSwVersion = fetchLatestSystemVariantString(upgradeVariables, "ptsSwVersion", "NULL", 10 * 60 * 1000);
        log.info("设置升级文件类型:{}, 执行时长:{}毫秒, 返回结果:{}", fileType, System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return ptsSwVersion;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
//        String version = "A1.22";
        String version = NotificationManager.getInstance().getVersion();
        long sTime = System.currentTimeMillis();
        boolean result = version.equals(ptsSwVersion);
        log.info("版本检测，执行时长:{}毫秒, 返回结果:{}", System.currentTimeMillis() - sTime, result ? "成功" : "失败");
        return result;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) {
//        Dispatch xcp = Dispatch.call(namespaces, "Item", "XCP").toDispatch();
//        Dispatch netWorksNodesVariables = getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
////        putVariant(netWorksNodesVariables, "xcpFlagName", String.format("%s_Flag", xcpName));     //设置ECU节点名称
////        putVariant(netWorksNodesVariables, "xcpFlag", 1);     //设置ECU节点名称
//
//
//        putCounter(netWorksNodesVariables, "xcpFlagCounter");
//        Dispatch xcpNamespaces = Dispatch.get(xcp, "Namespaces").toDispatch();  //获取变量组
//        Dispatch gwmACVariables = getVariantDispatch(xcpNamespaces, ecuNode, "Variables");
//        log.info("变量:{}", String.format("%s_Flag", xcpName));
//        putVariant(gwmACVariables, xcpName, xcpValue);
//        log.info("设置变量:{}的值:{}", xcpName, xcpName);
        Dispatch netWorksNodesVariables = VectorUtils.getVariantDispatch(namespaces, "NetWorksNodes", "Variables");
        VectorUtils.putVariant(netWorksNodesVariables, "xcpNode", String.format("%s::%s", "XCP", ecuNodeName));     //设置ECU节点名称
        VectorUtils.putVariant(netWorksNodesVariables, "xcpName", xcpName);     //设置ECU节点名称
        VectorUtils.putVariant(netWorksNodesVariables, "xcpValue", xcpValue);
        putCounter(netWorksNodesVariables, "xcpFlagCounter");
        return true;
    }

    @Override
    public boolean setCanLogName(String canLogName) throws BusError {
        return false;
    }

    @Override
    public boolean setCanLog(Integer deviceChannel, int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpFunSwitch(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpVar(String varName, int xcpValue) throws BusError {
        return false;
    }

    @Override
    public boolean setXcpSwitchAndVar(int switchCommand, String varName, int varValue) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyPosition(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setKeyButton(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setRDefogSts(int commandId) throws BusError {
        return false;
    }

    @Override
    public boolean setMirrorFoldSTS(String command) throws BusError {
        return false;
    }

    @Override
    public boolean setLampSwitch(String command) throws BusError {
        return false;
    }

    @Override
    public boolean checkTurnLamp(String turnLampType, int workTime, int checkPeriod) throws BusError {
        return false;
    }

    @Override
    public boolean checkFourDoor(String lockStatusCommand) throws BusError {
        return false;
    }

    @Override
    public boolean sendEventMsg(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) throws BusError {
        return false;
    }

    @Override
    public boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        return false;
    }

    @Override
    public double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError {
        return 0;
    }

    @Override
    public int fetchXcpVar(String varName) throws BusError {
        return 0;
    }

    @Override
    public boolean checkFindKeyOrNoKey(boolean findKey, int findKeyTime) throws BusError {
        return false;
    }


    @Override
    public String verifyCanMessage(Integer deviceChannel, String messageId, String byteData,Integer count) throws BusError {
        return "";
    }

    @Override
    public boolean wake(Integer deviceChannel,Integer time){
        return false;
    }

    @Override
    public boolean sleep(Integer deviceChannel,Integer time){
        return false;
    }

    @Override
    public void sendService(Integer deviceChannel, String messageId, String byteData) {

    }

    @Override
    public boolean sendDatas(Integer deviceChannel, String messageId, String byteData) {
        return false;
    }

    @Override
    public String checkReplyData(Integer deviceChannel, String messageId, String byteData) {
        return "";
    }

    @Override
    public boolean responsiveServices(UdsModel udsModel) {
        return false;
    }

    @Override
    public boolean responsiveService(Integer deviceChannel, String requestId, String responseId, String serviceData, String responseData) {
        return false;
    }

    @Override
    public String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError {
        return "";
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) {
        Dispatch xcp = Dispatch.call(namespaces, "Item", "XCP").toDispatch();
        Dispatch xcpNamespaces = Dispatch.get(xcp, "Namespaces").toDispatch();  //获取变量组
        Dispatch gwmACVariables = VectorUtils.getVariantDispatch(xcpNamespaces, ecuNodeName, "Variables");
        return fetchLatestSystemVariant(gwmACVariables, xcpName, 3000);
    }


    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.VECTOR_CAN;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
//        if (!canApp.isClose()) {
//            // 释放资源
//            canApp.safeRelease();
//            canApp.setClose(true);
//        }
        return true;
    }

    @Override
    protected boolean isAllowLock() {
        return false;
    }

    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        return true;
    }

    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        return true;
    }

    @Override
    public Map<String, DecodedSignal> fetchAllCanSignalValue(Integer deviceChannel, String messageName) throws BusError {
        return Collections.emptyMap();
    }


    public static void main(String[] args) {
        int i = Byte.toUnsignedInt((byte) 0);
        int j = Byte.toUnsignedInt((byte) 1);
        System.out.println("i---" + i);
        System.out.println("j---" + j);
    }
}
