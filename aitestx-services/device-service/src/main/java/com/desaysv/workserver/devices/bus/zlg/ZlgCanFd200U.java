package com.desaysv.workserver.devices.bus.zlg;

import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.FilterCanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.model.BusData;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import lombok.extern.slf4j.Slf4j;

/**
 * ZCAN_USBCANFD_200U底层接口
 */
@Slf4j
public class ZlgCanFd200U extends ZlgCan {

    private final ZlgCanAbstractionLayer can;

    public ZlgCanFd200U(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        can = new ZlgCanAbstractionLayer();
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Bus.ZLG_USBCANFD_200U;
    }


//    @Override
//    public CyclicTask sendPeriodic(CanMessage message, float period, Float duration) {
//        can.sendPeriod(getChannelHandle(message.getChannel()), message);
//        return null;
//    }
//
//    @Override
//    public CyclicTask stopCanMessage(Integer deviceChannel, Integer messageId) {
//        ZlgApi.stopCanPeriodMessage(getChannelHandle(deviceChannel),deviceChannel,messageId);
//        log.info("{}停止CAN通道{}报文:0x{}", getDeviceName(), deviceChannel, String.format("%X", messageId));
//        return null;
//    }

    @Override
    public void send(CanMessage message, Float timeout) throws BusError {
        boolean isSendOk;
        if (timeout != null) {
            //阻塞发送
            isSendOk = can.blockingSend(message, timeout);
        } else {
            //正常发送
            isSendOk = can.send(getChannelHandle(message.getChannel()), message);
        }

        if (!isSendOk) {
            throw new BusError(String.format("could not send message:%s", message));
        }

    }

    @Override
    public FilterCanMessage recvInternal(Integer channel, Float timeout) throws BusError {
        return null;
    }


    @Override
    public boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError {
        BusData busData = getZlg200UBusDataByJsonConfig(deviceChannel);
        return sendAllIGModuleMessages(busData, deviceChannel, command);
    }


    @Override
    public boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError {
        BusData busData = getZlg200UBusDataByJsonConfig(deviceChannel);
        return sendSingleIGModuleMessages(busData, deviceChannel, igTabName, command);
    }

    private BusData getZlg200UBusDataByJsonConfig(Integer deviceChannel) {
        String filePath = String.format("D:\\FlyTest\\data\\client\\projects\\%s\\config\\dbc\\%s\\channel%d\\data.json", projectName, getDeviceName(), deviceChannel);
        return importBusDataFile(filePath);
    }
}
