package com.desaysv.workserver.devices.bus.interfaces;

import com.alibaba.fastjson2.JSON;
import com.desaysv.workserver.action_sequence.ActualExpectedResult;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import org.apache.commons.codec.binary.Hex;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.TEST_RESULT_FAIL;
import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.TEST_RESULT_PASS;
import static com.desaysv.workserver.utils.DtcValidatorUtils.checkDtcExistence;
import static com.desaysv.workserver.utils.DtcValidatorUtils.checkDtcStatus;
import static com.desaysv.workserver.utils.StrUtils.compareStrings;

public interface IVectorCanSequence {
    Logger log = LogManager.getLogger(IVectorCanSequence.class.getSimpleName());

    boolean setCanFBL(String fblCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_FBL"})
    default ActualExpectedResult setCanFBLInfo(String fblCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanFBL(fblCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中FBL Case编号:{},检测结果:{},共耗时:{}毫秒", fblCaseIdString, pass ? "通过" : "失败",
                System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setFBLInfo", pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean setCanNodeM(String nodeMCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_NODEM"})
    default ActualExpectedResult setCanNodeMInfo(String nodeMCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanNodeM(nodeMCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中NodeM Case编号:{},检测结果:{},共耗时:{}毫秒", nodeMCaseIdString, pass ? "通过" : "失败",
                System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanNodeMInfo", pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }


    boolean setCanNM(String nmCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_NM"})
    default ActualExpectedResult setCanNMInfo(String nmCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanNM(nmCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中NM Case编号:{},检测结果:{},共耗时:{}毫秒", nmCaseIdString, pass ? "通过" : "失败",
                System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanNMInfo", pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean setCanUdsKeepSession(String status) throws BusError;
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_UDS_KEEP_SESSION"})
    default ActualExpectedResult setCanUdsKeepSessionInfo(String status) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanUdsKeepSession(status);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中Uds KeepSession指令:{},检测结果:{},共耗时:{}毫秒", status, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanUdsKeepSessionInfo", pass, status);
        return actualExpectedResult;
    }



    boolean setCanUDSFun(String address, String udsCaseIdString) throws BusError;
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_UDS_FUN"})
    default ActualExpectedResult setCanUDSFunInfo(String address, String udsCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            //去掉udsCaseIdString所有空格
            udsCaseIdString = udsCaseIdString.replaceAll("\\s", "");
            pass = setCanUDSFun(address, udsCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中UDS Case指令:{},检测结果:{},共耗时:{}毫秒", udsCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanUDSInfo", pass, udsCaseIdString);
        return actualExpectedResult;
    }

    boolean setCanUDS27ServerFun(String address, String udsCaseIdString, int flag) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_UDS_27_SERVER_FUN"})
    default ActualExpectedResult setCanUDS27ServerInfo(String address, String udsCaseIdString, int flag) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            //去掉udsCaseIdString所有空格
            udsCaseIdString = udsCaseIdString.replaceAll("\\s", "");
            pass = setCanUDS27ServerFun(address, udsCaseIdString, flag);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中UDS 27服务，指令:{},检测结果:{},共耗时:{}毫秒", udsCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanUDSInfo", pass, udsCaseIdString);
        return actualExpectedResult;
    }


    boolean setCanUdsUdp(String udsUdpCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_UDS_UDP"})
    default ActualExpectedResult setCanUdsUdpInfo(String udsUdpCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanUdsUdp(udsUdpCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中UDS_UDP Case编号:{},检测结果:{},共耗时:{}毫秒", udsUdpCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanUdsUdpInfo", pass, udsUdpCaseIdString);
        return actualExpectedResult;
    }

    boolean setCanDataLink(String dataLinkCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_DATA_LINK"})
    default ActualExpectedResult setCanDataLinkInfo(String dataLinkCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanDataLink(dataLinkCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中DataLink Case编号:{},检测结果:{},共耗时:{}毫秒", dataLinkCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanDataLinkInfo", pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean setCanTP(String tpCaseIdString) throws BusError;
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_TP"})
    default ActualExpectedResult setCanTPInfo(String tpCaseIdString) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanTP(tpCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN中TP Case编号:{},检测结果:{},共耗时:{}毫秒", tpCaseIdString, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanTPInfo", pass, tpCaseIdString);
        return actualExpectedResult;
    }

    boolean setCanUdsLogName(String udsLogName);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_UDS_LOG_NAME"})
    default ActualExpectedResult setCanUdsLogNameInfo(String udsLogName) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = setCanUdsLogName(udsLogName);
        actualExpectedResult.put("setCanUdsLogNameInfo", pass, udsLogName);
        log.info("执行CAN中setCanUdsLogName udsLogName:{},检测结果:{},共耗时:{}毫秒", udsLogName, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setCanUdsLog(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_UDS_LOG"})
    default ActualExpectedResult setCanUdsLogInfo(int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setCanUdsLog(commandId);
//            throw new BusError("人为制造异常，查看前端测试窗口颜色渲染~~");
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("执行CAN诊断Log:{},检测结果:{},共耗时:{}毫秒", commandId == 0 ? "开始录制" : "结束录制", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanUdsLogInfo", pass, commandId);
        return actualExpectedResult;
    }

    boolean fetchCanFBL(String fblCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_FBL"})
    default ActualExpectedResult fetchCanFBLInfo(String fblCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanFBL(fblCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN FBL Case编号:{},检测结果:{},共耗时:{}毫秒", fblCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanFBLInfo", expectPass == pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean fetchCanNodeM(String nodeMCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_NODEM"})
    default ActualExpectedResult fetchCanNodeMInfo(String nodeMCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanNodeM(nodeMCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN NodeM Case编号:{},检测结果:{},共耗时:{}毫秒", nodeMCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanNodeMInfo", expectPass == pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean fetchCanNM(String nmCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_NM"})
    default ActualExpectedResult fetchCanNMInfo(String nmCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanNM(nmCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN NM Case编号:{},检测结果:{},共耗时:{}毫秒", nmCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanNMInfo", expectPass == pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }


    boolean fetchCanDataLink(String dataLinkCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_DATA_LINK"})
    default ActualExpectedResult fetchCanDataLinkInfo(String dataLinkCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanDataLink(dataLinkCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN DataLink Case编号:{},检测结果:{},共耗时:{}毫秒", dataLinkCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanDataLinkInfo", expectPass == pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    boolean fetchCanTP(String tpCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_TP"})
    default ActualExpectedResult fetchCanTPInfo(String tpCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanTP(tpCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN TP Case编号:{},检测结果:{},共耗时:{}毫秒", tpCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanTPInfo", expectPass == pass, pass ? TEST_RESULT_PASS : TEST_RESULT_FAIL);
        return actualExpectedResult;
    }

    String fetchCanUdsDataExplainFun() throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_UDS_FUN_CHECK_NOT_DTC"})
    default ActualExpectedResult fetchCanUdsFunNotDTCInfo(String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String result = null;
        try {
            result = fetchCanUdsDataExplainFun();
            pass = !checkDtcExistence(expectResult, result);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("fetchCanUdsFunNotDTC期望:{},检测结果:{},共耗时:{}毫秒", expectResult, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsFunInfo", pass, pass ? "无期望DTC" : expectResult);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_UDS_FUN_CHECK_DTC"})
    default ActualExpectedResult fetchCanUdsFunWithDTCInfo(String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String result = null;
        try {
            result = fetchCanUdsDataExplainFun();
            pass = checkDtcStatus(expectResult, result);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取fetchCanUdsFunWithDTC期望:{},检测结果:{},共耗时:{}毫秒", expectResult, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsFunWithDTCInfo", pass, pass ? expectResult : "无期望DTC" );
        return actualExpectedResult;
    }

    String fetchCanUds22ExplainFun() throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_UDS_EXPLAIN_FUN"})
    default ActualExpectedResult fetchCanUdsExplainFunInfo(String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String result = null;
        try {
            result = fetchCanUds22ExplainFun();
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        boolean pass = expectResult.equals(result);
        log.info("获取CAN UDS_EXPLAIN_FUN Case编号:{},检测结果:{},共耗时:{}毫秒", result, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsExplainFunInfo", pass, result);
        return actualExpectedResult;
    }

    boolean fetchCanUdsUdp(String udsUdpCaseIdString) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_UDS_UDP"})
    default ActualExpectedResult fetchCanUdsUdpInfo(String udsUdpCaseIdString, int expectResult) {
        boolean expectPass = expectResult == 1;
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = fetchCanUdsUdp(udsUdpCaseIdString);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取CAN UdsUdp Case编号:{},检测结果:{},共耗时:{}毫秒", udsUdpCaseIdString, expectPass == pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("fetchCanUdsUdpInfo", expectPass == pass, udsUdpCaseIdString);
        return actualExpectedResult;
    }

    boolean setStartUdsTest(int flag) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_START_UDS_TEST"})
    default ActualExpectedResult setStartUdsTestInfo(int flag) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setStartUdsTest(flag);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setStartUdsTestInfo", pass, flag);
        log.info("执行setStartUdsTestInfo flag:{},检测结果:{},共耗时:{}毫秒", flag == 1 ? "开启UDS" : "停止UDS", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setResetCharge(int flag) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_RESET_CHARGE"})
    default ActualExpectedResult setResetChargeInfo(int flag) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setResetCharge(flag);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setResetChargeInfo", pass, flag);
        log.info("执行setResetChargeInfo flag:{},检测结果:{},共耗时:{}毫秒", flag == 1 ? "重启开关面板" : "关闭开关面板", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setTemperature(int mode, double value) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_TEMPERATURE"})
    default ActualExpectedResult setTemperatureInfo(int mode, int value) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setTemperature(mode, value);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setTemperatureInfo", pass, value);
        log.info("setTemperatureInfo mode:{},value:{} ,检测结果:{},共耗时:{}毫秒", mode, value, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setPowerState(String powerState) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_POWER_STATE"})
    default ActualExpectedResult setPowerStateInfo(Integer deviceChannel, String powerState) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setPowerState(powerState);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setPowerStateInfo", pass, powerState);
        log.info("setPowerStateInfo powerState:{} ,检测结果:{},共耗时:{}毫秒", powerState, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    boolean setBoardCardInit(String initType) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_BOARD_CARD_INIT"})
    default ActualExpectedResult setBoardCardInitInfo(Integer deviceChannel, String initType) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            pass = setBoardCardInit(initType);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("setBoardCardInitInfo", pass, initType);
        log.info("setBoardCardInitInfo initType:{} ,检测结果:{},共耗时:{}毫秒", initType, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }
}
