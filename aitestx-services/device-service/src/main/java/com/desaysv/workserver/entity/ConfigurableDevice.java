package com.desaysv.workserver.entity;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.config.DeviceConfig;
import com.desaysv.workserver.devices.DeviceConfigurable;
import com.desaysv.workserver.filemanager.project.DeviceFileManager;
import com.desaysv.workserver.filemanager.project.ProjectFileManager;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@Slf4j
public abstract class ConfigurableDevice<T extends DeviceConfig> extends Device implements DeviceConfigurable<T> {

    @JSONField(serialize = false)
    private T deviceConfig;

    /**
     * 导入项目设备配置
     *
     * @param projectName
     * @param tClass
     * @return
     */
    public T loadConfigByProject(String projectName, Class<T> tClass) {
        log.info("加载配置类：{}", tClass);
        if (projectName == null || getDeviceConfigClass() == null) {
            return null;
        }
        DeviceFileManager fileManager = ProjectFileManager.of(projectName, DeviceFileManager.class);
        String jsonText = fileManager.readDeviceConfig(getDeviceType(), getDeviceModel());
        T deviceConfig = new Gson().fromJson(jsonText, tClass);
        if (deviceConfig == null) {
            try {
                deviceConfig = tClass.newInstance();
                deviceConfig.setProject(projectName);
            } catch (InstantiationException | IllegalAccessException e) {
                log.error(e.getMessage(), e);
            }
        }
        setDeviceConfig(deviceConfig);
        log.info("导入设备配置:{}", deviceConfig);
        return deviceConfig;
    }

    public ConfigurableDevice() {
        this(new DeviceOperationParameter());
    }

    public ConfigurableDevice(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }

    @JsonIgnore
    @JSONField(serialize = false)
    @SuppressWarnings("unchecked")
    public Class<T> getDeviceConfigClass() {
        // 获取当前类的泛型超类
        java.lang.reflect.Type genericSuperclass = getClass().getGenericSuperclass();

        // 如果是参数化类型（即包含泛型参数）
        if (genericSuperclass instanceof java.lang.reflect.ParameterizedType) {
            java.lang.reflect.ParameterizedType paramType = (java.lang.reflect.ParameterizedType) genericSuperclass;
            // 获取实际的泛型参数类型
            java.lang.reflect.Type[] typeArguments = paramType.getActualTypeArguments();
            if (typeArguments.length > 0 && typeArguments[0] instanceof Class) {
                return (Class<T>) typeArguments[0];
            }
        }

        // 如果无法通过反射获取，则尝试从父类的泛型信息获取
        if (getClass().getSuperclass() != ConfigurableDevice.class &&
                ConfigurableDevice.class.isAssignableFrom(getClass().getSuperclass())) {
            try {
                // 获取父类的泛型类型信息
                java.lang.reflect.Type parentGenericSuperclass = getClass().getSuperclass().getGenericSuperclass();
                if (parentGenericSuperclass instanceof java.lang.reflect.ParameterizedType) {
                    java.lang.reflect.ParameterizedType parentParamType =
                            (java.lang.reflect.ParameterizedType) parentGenericSuperclass;
                    java.lang.reflect.Type[] parentTypeArguments = parentParamType.getActualTypeArguments();
                    if (parentTypeArguments.length > 0 && parentTypeArguments[0] instanceof Class) {
                        return (Class<T>) parentTypeArguments[0];
                    }
                }
            } catch (Exception e) {
                log.error("无法从父类获取设备配置类型", e);
            }
        }

        log.warn("无法确定设备配置类型，请在子类中覆盖getDeviceConfigClass方法");
        return null;
    }

    @Override
    public T loadConfig(String projectName) {
        if (deviceConfig == null) {
            return loadConfigByProject(projectName, getDeviceConfigClass());
        }
        return getDeviceConfig();
    }

    @Override
    public void updateConfig(T deviceConfig) {
        //FIXME: 区分设备第几个
        DeviceFileManager fileManager = ProjectFileManager.of(deviceConfig.getProject(), DeviceFileManager.class);
        fileManager.writeDeviceConfig(getDeviceType(), JSON.toJSONString(deviceConfig), getDeviceModel());
        setDeviceConfig(deviceConfig);
        log.info("更新设备配置:{}", deviceConfig);
    }

    @Override
    public void updateConfigByKey(DeviceConfig deviceConfig) {
        if (deviceConfig == null || deviceConfig.getConfigKey() == null) {
            log.warn("设备配置或配置键为空，无法更新");
            return;
        }

        String key = deviceConfig.getConfigKey();
        String value = deviceConfig.getConfigValue();
        String projectName = deviceConfig.getProject();

        // 确保当前设备配置已加载
        T currentConfig = getDeviceConfig();
        if (currentConfig == null) {
            currentConfig = loadConfig(projectName);
            if (currentConfig == null) {
                log.error("无法加载设备配置，项目名: {}", projectName);
                return;
            }
        }

        // 获取当前配置的JSON表示
        String jsonConfig = JSON.toJSONString(currentConfig);

        // 将JSON字符串转换为通用的Map对象
        com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(jsonConfig);

        // 添加或更新键值对
        jsonObject.put(key, value);

        // 将更新后的JSON对象转换回设备配置对象
        T updatedConfig = JSON.parseObject(jsonObject.toJSONString(), (Class<T>) currentConfig.getClass());

        // 保存更新后的配置
        DeviceFileManager fileManager = ProjectFileManager.of(projectName, DeviceFileManager.class);
        fileManager.writeDeviceConfig(getDeviceType(), jsonObject.toJSONString(), getDeviceModel());

        // 更新内存中的配置
        setDeviceConfig(updatedConfig);

        log.info("更新设备键值对:{}->{}", key, value);
    }

    @Override
    public String loadConfigByKey(DeviceConfig deviceConfig) {
        String projectName = deviceConfig.getProject();
        String key = deviceConfig.getConfigKey();
        if (projectName == null || key == null) {
            log.warn("项目名或键为空，无法加载配置值");
            return "";
        }

        // 确保当前设备配置已加载
        T currentConfig = getDeviceConfig();
        if (currentConfig == null) {
            currentConfig = loadConfig(projectName);
            if (currentConfig == null) {
                log.error("无法加载设备配置，项目名: {}", projectName);
                return "";
            }
        }

        // 获取当前配置的JSON表示
        String jsonConfig = JSON.toJSONString(currentConfig);

        // 将JSON字符串转换为通用的Map对象
        com.alibaba.fastjson2.JSONObject jsonObject = JSON.parseObject(jsonConfig);

        // 检查是否存在指定的键
        if (jsonObject.containsKey(key)) {
            Object value = jsonObject.get(key);
            return value != null ? value.toString() : "";
        }

        // 如果键不存在，返回空字符串
        log.debug("未找到键 {} 的配置值", key);
        return "";
    }
}
