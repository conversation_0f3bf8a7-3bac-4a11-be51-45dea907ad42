package com.desaysv.workserver.devices.bus.zlg;

import cantools.dbc.DbcReader;
import cantools.dbc.DecodedSignal;
import cantools.dbc.Message;
import cantools.dbc.Signal;
import cantools.exceptions.DecodingFrameLengthException;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.annotation.JSONField;
import com.desaysv.workserver.canlog.blflog.exception.BlfException;
import com.desaysv.workserver.canlog.queue.FixedSizeQueue;
import com.desaysv.workserver.canlog.service.CanLogService;
import com.desaysv.workserver.canlog.service.impl.CanLogServiceImpl;
import com.desaysv.workserver.config.can.CanConfig;
import com.desaysv.workserver.config.can.CanConfigParameter;
import com.desaysv.workserver.config.can.DbcConfig;
import com.desaysv.workserver.config.can.NetCanConfigParameter;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.devices.bus.base.can.CanLogRealTimeSaveParameter;
import com.desaysv.workserver.devices.bus.base.can.CanMessage;
import com.desaysv.workserver.devices.bus.base.can.CanMessageRealTimeSave;
import com.desaysv.workserver.devices.bus.base.can.SequenceableCanBus;
import com.desaysv.workserver.devices.bus.base.frexray.FlexrayMessage;
import com.desaysv.workserver.devices.bus.nican.CanMessageVo;
import com.desaysv.workserver.devices.bus.nican.CanSignalVo;
import com.desaysv.workserver.exceptions.can.ZlgCanExecuteException;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.ByteUtils;
import com.desaysv.workserver.utils.sse.SseConstants;
import com.desaysv.workserver.utils.sse.SseUtils;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 周立功Can底层接口
 * <p>
 * 固件最高支持版本如下
 * 带LIN：2.4.6
 * 不带LIN：1.1.8
 * </p>
 */
@Slf4j
public abstract class ZlgCan extends SequenceableCanBus {

    private final Map<Integer, Pointer> channelHandleMap = new ConcurrentHashMap<>();
    private Pointer deviceHandle; // CAN设备句柄
    private CanLogService canLogService;
    private FixedSizeQueue<CanMessageVo> fixedQueue;
    private static final Lock lock = new ReentrantLock();//静态锁 类级别锁 保证即便多个zlgcan实例也只有一个能同时open
    private static final Condition condition = lock.newCondition();
    private ZlgApi readZlgApi;
    private ZlgApi readDBCCanApi;
    private ZlgApi readFrameCanApi;
    private ZlgApi readChannelFrameApi;
    private boolean isOpen;
    private ZlgCircularBuffer<CanMessageVo> rxBuffer;

    public ZlgCan(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
    }


    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean open() throws DeviceOpenException {
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if (canConfig != null) {
            canConfig.getConfigParameters().clear();
            updateConfig(canConfig);
        }
        return openDevice();
    }

    /**
     * 自动打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean autoOpen() throws DeviceOpenException {
        //改成从CanConfig获取
        CanConfig canConfig = loadConfigByProject(getDeviceOperationParameter().getProject(), CanConfig.class);
        if (canConfig == null) {
            throw new DeviceOpenException("周立功配置文件不存在，请删除设备重新连接");
        }
        Map<String, CanConfigParameter> configParameters = canConfig.getConfigParameters();
        Map<String, NetCanConfigParameter> configNetParameters = canConfig.getConfigNetParameters();
        if (CollectionUtils.isEmpty(configParameters) && CollectionUtils.isEmpty(configNetParameters)) {
            throw new DeviceOpenException("周立功配置文件不含通道，请删除设备重新连接");
        }
        boolean isOpenOk = openDevice();

        if (isOpenOk) {
            if (!CollectionUtils.isEmpty(configParameters)) {
                Object ch0OpenParams = configParameters.get("1");
                Object ch1OpenParams = configParameters.get("2");
                if (ch0OpenParams != null) {
                    //打开通道1
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch0OpenParams), CanConfigParameter.class), canConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道2
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch1OpenParams), CanConfigParameter.class), canConfig);
                }
            }
            if (!CollectionUtils.isEmpty(configNetParameters)) {
                Object ch0OpenParams = configNetParameters.get("1");
                Object ch1OpenParams = configNetParameters.get("2");
                Object ch2OpenParams = configNetParameters.get("3");
                Object ch3OpenParams = configNetParameters.get("4");
                if (!isOpen) {
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_mode", ((NetCanConfigParameter) ch0OpenParams).getWorkMode());
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/ip", ((NetCanConfigParameter) ch0OpenParams).getIp());
                    ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_port", ((NetCanConfigParameter) ch0OpenParams).getWorkPort());
                    isOpen = true;
                }
                if (ch0OpenParams != null) {
                    //打开通道1
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch0OpenParams), NetCanConfigParameter.class), canConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道2
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch1OpenParams), NetCanConfigParameter.class), canConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道3
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch2OpenParams), NetCanConfigParameter.class), canConfig);
                }
                if (ch1OpenParams != null) {
                    //打开通道4
                    openChannelInternal(JSON.parseObject(JSON.toJSONString(ch3OpenParams), NetCanConfigParameter.class), canConfig);
                }
            }

        }
        return isOpenOk;
    }

    /**
     * 打开设备
     *
     * @return
     * @throws DeviceOpenException
     */
    private boolean openDevice() throws DeviceOpenException {
        lock.lock();
        try {
            if (deviceHandle != null) {
                log.info("周立功设备已连接:{}", getDeviceName());
                return true;
            }

            if (!isSimulated()) {
                Integer deviceType = (Integer) ZlgCanConstants.getConstantsMap().get(getDeviceModel());
                if (deviceType == null) {
                    throw new DeviceOpenException(String.format("周立功CAN设备型号未知:%s", getDeviceModel()));
                }
                deviceHandle = ZlgApi.openDevice(deviceType, getDevicePort());
                if (deviceHandle == null) {
                    throw new DeviceOpenException(String.format("周立功%s连接失败，请检查CAN盒子是否连接正常", getDeviceName()));
                }
                // 将设备句柄添加到设备管理器中
                ZlgUtil.getInstance().addDeviceHandle(deviceType + "-" + getDevicePort(), deviceHandle);
                //打开设备
                ZlgCanLib.ZCAN_DEVICE_INFO deviceInfo = ZlgApi.getDeviceInfo(deviceHandle);
                if (deviceInfo != null) {
                    log.info("周立功{}设备信息:{}", getDeviceName(), ZlgDeviceInfo.format(deviceInfo));
                }
            }
            condition.signalAll();
            return true;
        } finally {
            rxBuffer = new ZlgCircularBuffer<>(1000);//注册canFD回调包含canFD和can报文
            lock.unlock();
        }
    }


    /**
     * 打开通道
     *
     * @param canConfigParameter
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean openChannel(CanConfigParameter canConfigParameter) throws DeviceOpenException {
        //写入配置文件
        CanConfig canConfig = writeConfig(canConfigParameter);
        if (deviceHandle == null) {
            //未打开设备尝试打开设备
            openDevice();
        }
        return openChannelInternal(canConfigParameter, canConfig);
    }

    /**
     * 打开通道
     *
     * @param netCanConfigParameter
     * @return
     * @throws DeviceOpenException
     */
    @Override
    public boolean openChannel(NetCanConfigParameter netCanConfigParameter) throws DeviceOpenException {
        //写入配置文件
        CanConfig canConfig = writeConfig(netCanConfigParameter);
        if (deviceHandle == null) {
            //未打开设备尝试打开设备
            openDevice();
        }
        if (!isOpen) {
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_mode", netCanConfigParameter.getWorkMode());
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/ip", netCanConfigParameter.getIp());
            ZlgCanLib.Instance.ZCAN_SetValue(deviceHandle, "0/work_port", netCanConfigParameter.getWorkPort());
            isOpen = true;
        }
        return openChannelInternal(netCanConfigParameter, canConfig);
    }

    /**
     * 获取最大通道数
     *
     * @return 最大通道数
     */
    @Override
    public int getMaxChannelCount() {
        return 2;
    }

    /**
     * 打开通道
     *
     * @param canConfigParameter
     * @param canConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(CanConfigParameter canConfigParameter, CanConfig canConfig) throws DeviceOpenException {
        lock.lock();
        try {
            if (!isSimulated()) {
                try {
                    int channel = canConfigParameter.getChannel();
                    if (channel == -1) {
                        //启动通道1
                        startChannel(canConfig.getConfigParameters().get("1"));
                        //启动通道2
                        startChannel(canConfig.getConfigParameters().get("2"));
                    } else {
                        startChannel(canConfigParameter);
                    }
                } catch (ZlgCanExecuteException e) {
                    setChannelConfigured(false);
                    throw new DeviceOpenException(e);
                }
            }
        } finally {
            lock.unlock();
        }
        setChannelConfigured(true);
        return true;
    }

    /**
     * 打开通道
     *
     * @param netCanConfigParameter
     * @param canConfig
     * @return
     * @throws DeviceOpenException
     */
    private boolean openChannelInternal(NetCanConfigParameter netCanConfigParameter, CanConfig canConfig) throws DeviceOpenException {
        lock.lock();
        try {
            if (!isSimulated()) {
                try {
                    int channel = netCanConfigParameter.getChannel();
                    if (channel == -1) {
                        //启动通道1
                        startChannel(canConfig.getConfigNetParameters().get("1"));
                        //启动通道2
                        startChannel(canConfig.getConfigNetParameters().get("2"));
                        //启动通道3
                        startChannel(canConfig.getConfigNetParameters().get("3"));
                        //启动通道4
                        startChannel(canConfig.getConfigNetParameters().get("4"));
                    } else {
                        startChannel(netCanConfigParameter);
                    }
                } catch (ZlgCanExecuteException e) {
                    setChannelConfigured(false);
                    throw new DeviceOpenException(e);
                }
            }
        } finally {
            lock.unlock();
        }
        setChannelConfigured(true);
        return true;
    }

    /**
     * 启动通道
     *
     * @param canConfigParameter 通道参数
     * @return
     * @throws ZlgCanExecuteException
     */
    private boolean startChannel(CanConfigParameter canConfigParameter) throws ZlgCanExecuteException {
        if (getChannelHandle(canConfigParameter.getChannel()) != null) {
            log.info("周立功{}通道{}已打开", getDeviceName(), canConfigParameter.getChannel());
            return true;
        }
        log.info("打开周立功{}通道{}:\n{}",
                getDeviceName(),
                canConfigParameter.getChannel(),
                ToStringBuilder.reflectionToString(canConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        Pointer channelHandle = ZlgApi.startCan(deviceHandle, canConfigParameter);
        channelHandleMap.put(canConfigParameter.getChannel(), channelHandle);
        startChannelReceiver(canConfigParameter.getChannel());
        return true;
    }

    /**
     * 启动通道
     *
     * @param netCanConfigParameter 通道参数
     * @return
     * @throws ZlgCanExecuteException
     */
    private boolean startChannel(NetCanConfigParameter netCanConfigParameter) throws ZlgCanExecuteException {
        if (getChannelHandle(netCanConfigParameter.getChannel()) != null) {
            log.info("周立功{}通道{}已打开", getDeviceName(), netCanConfigParameter.getChannel());
            return true;
        }
        log.info("打开周立功{}通道{}:\n{}", getDeviceName(), netCanConfigParameter.getChannel(), ToStringBuilder.reflectionToString(netCanConfigParameter, ToStringStyle.MULTI_LINE_STYLE));
        Pointer channelHandle = ZlgApi.startCan(deviceHandle, netCanConfigParameter);
        channelHandleMap.put(netCanConfigParameter.getChannel(), channelHandle);
        startChannelReceiver(netCanConfigParameter.getChannel());
        return true;
    }

    /**
     * 获取通道句柄
     *
     * @param channel
     * @return
     */
    @JSONField(serialize = false)
    protected Pointer getChannelHandle(int channel) {
        return channelHandleMap.get(channel);
    }

    /**
     * 关闭通道
     *
     * @param channel
     * @return
     * @throws DeviceCloseException
     */
    @Override
    public boolean closeChannel(int channel) throws DeviceCloseException {
        lock.lock();
        try {
            if (isSimulated()) {
                return true;
            }
            boolean isOk;
            try {
                if (channel == -1) {
                    isOk = ZlgApi.stopChannel(channelHandleMap.get(1), 1)
                            & ZlgApi.stopChannel(channelHandleMap.get(2), 2);
                    channelHandleMap.clear();
                    return isOk;
                }
                isOk = ZlgApi.stopChannel(channelHandleMap.get(channel), channel);
                channelHandleMap.remove(channel);
                return isOk;
            } catch (ZlgCanExecuteException e) {
                throw new DeviceCloseException(e);
            }
        } finally {
            try {
                stopChannelFrameReceiver(channel);
            } catch (Exception e){
                log.error("关闭读取周立功{}通道{}失败！", getDeviceName(), channel);
            }
            lock.unlock();
        }

    }

    /**
     * 关闭设备
     *
     * @return
     * @throws DeviceCloseException
     */
    @Override
    public boolean close() throws DeviceCloseException {
        lock.lock();
        try {
            log.info("关闭周立功{}", getDeviceName());
            boolean isOk = super.close();
            if (!isSimulated()) {
                //关闭所有还没关闭的通道
                for (Map.Entry<Integer, Pointer> entry : channelHandleMap.entrySet()) {
                    try {
                        ZlgApi.stopChannel(entry.getValue(), entry.getKey());
                    } catch (ZlgCanExecuteException e) {
                        throw new DeviceCloseException(e);
                    }
                }
                channelHandleMap.clear();
                // 删除已存在的设备句柄
                Integer deviceType = (Integer) ZlgCanConstants.getConstantsMap().get(getDeviceModel());
                ZlgUtil.getInstance().removeDeviceHandle(deviceType + "-" + getDevicePort());
                if (deviceHandle != null) {
                    isOk &= ZlgApi.closeDevice(deviceHandle);
                    deviceHandle = null;
                }
            }
            isOpen = false;
            return isOk;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 启动CAN接收
     *
     * @param deviceChannel
     * @param dbcConfig
     */
    @Override
    public void startDbcReceiver(Integer deviceChannel, DbcConfig dbcConfig) {
        log.info("读取周立功{}通道{}", getDeviceName(), deviceChannel);
        List<Message> dbcMessages = new ArrayList<>();
        for (String s : dbcConfig.getDbcPaths()) {
            DbcReader dbcReader = new DbcReader();
            dbcReader.parseFile(new File(s));
            dbcMessages.addAll(dbcReader.getBus().getMessages());
        }
        if (readDBCCanApi == null) {
            readDBCCanApi = new ZlgApi();
        }
        readDBCCanApi.read(channelHandleMap.get(deviceChannel), list -> {
            List<CanMessageVo> canMessageVos = fetchData(list, dbcMessages);
            if (!CollectionUtils.isEmpty(canMessageVos)) {
                for (CanMessageVo canMessageVo : canMessageVos) {
                    canMessageVo.setChn(String.valueOf(deviceChannel)); //设置channel
                }
                String jsonString = JSON.toJSONString(canMessageVos);
                SseUtils.pubMsg(SseConstants.CAN_DBC_RECEIVER_LIST + deviceChannel, jsonString);
                //实时保存
                if (canLogService != null) {
                    canLogService.realTimeSaveData(canMessageVos);
                }
            }
        });
    }

    @Override
    public void startFrameReceiver(Integer deviceChannel) {
        log.info("读取周立功{}通道{}", getDeviceName(), deviceChannel);
        fixedQueue = new FixedSizeQueue<>(99999);
        if (readFrameCanApi == null) {
            readFrameCanApi = new ZlgApi();
        }
        readFrameCanApi.read(channelHandleMap.get(deviceChannel), list -> {
            List<CanMessageVo> canMessageVos = fetchData(list);
            if (!CollectionUtils.isEmpty(canMessageVos)) {
                for (CanMessageVo canMessageVo : canMessageVos) {
                    canMessageVo.setChn(String.valueOf(deviceChannel)); //设置channel
                }
                String jsonString = JSON.toJSONString(canMessageVos);
                SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER_LIST + deviceChannel, jsonString);
                //帧保存数据
                try {
                    fixedQueue.addAll(canMessageVos);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                //实时保存
                if (canLogService != null) {
                    canLogService.realTimeSaveData(canMessageVos);
                }
            }
        });
    }

    public void startChannelReceiver(Integer deviceChannel) {
        log.info("读取周立功{}通道{}", getDeviceName(), deviceChannel);
        if (readChannelFrameApi == null) {
            readChannelFrameApi = new ZlgApi();
        }
        readChannelFrameApi.read(channelHandleMap.get(deviceChannel), list -> {
            List<CanMessageVo> canMessageVos = fetchData(list);
            if (!CollectionUtils.isEmpty(canMessageVos)) {
                for (CanMessageVo canMessageVo : canMessageVos) {
                    canMessageVo.setChn(String.valueOf(deviceChannel)); //设置channel
                    // 将接收到的报文存入CircularBuffer
                    rxBuffer.write(canMessageVo);
                }
                String jsonString = JSON.toJSONString(canMessageVos);
                SseUtils.pubMsg(SseConstants.CAN_FRAME_RECEIVER_LIST + deviceChannel, jsonString);
            }
        });
    }

    public void stopChannelFrameReceiver(Integer deviceChannel) throws ZlgCanExecuteException {
        log.info("关闭读取周立功{}通道{}", getDeviceName(), deviceChannel);
        readChannelFrameApi.stopRead(channelHandleMap.get(deviceChannel), deviceChannel);
    }

    @Override
    public void stopFrameReceiver(Integer deviceChannel) throws ZlgCanExecuteException {
        log.info("关闭读取周立功{}通道{}", getDeviceName(), deviceChannel);
        readFrameCanApi.stopRead(channelHandleMap.get(deviceChannel), deviceChannel);
    }


    @Override
    public void stopDbcReceiver(Integer deviceChannel) throws ZlgCanExecuteException {
        log.info("关闭读取周立功{}通道{}", getDeviceName(), deviceChannel);
        readDBCCanApi.stopRead(channelHandleMap.get(deviceChannel), deviceChannel);
    }


    private List<CanMessageVo> fetchData(List<ZlgCanReturnData> list, List<Message> dbcMessages) {
        // 转换纳秒为秒
        List<CanMessageVo> canMessageVos = new ArrayList<>();
        for (ZlgCanReturnData zlgCanReturnData : list) {
            String id = String.format("0x%02X", zlgCanReturnData.getId());
            Message message = dbcMessages.stream().filter(o -> o.getId().equals(id)).findFirst().orElse(null);
            if (message != null) {
                try {
                    CanMessageVo canMessageVo = new CanMessageVo();
                    canMessageVo.setTime(Double.parseDouble(String.format("%.6f", zlgCanReturnData.getTimestamp() / 1_000_000.0)));
                    canMessageVo.setId(id);
                    //canMessage.setChn(canR.getChn());
                    canMessageVo.setEventType(zlgCanReturnData.getEventType());
                    canMessageVo.setDir(zlgCanReturnData.getDir());
                    canMessageVo.setDlc(zlgCanReturnData.getDlc());
                    //这里如果出现长度不一致的情况直接截断或者补00
                    byte[] validPayload = Arrays.copyOf(zlgCanReturnData.getData(), message.getLength());
                    canMessageVo.setData(validPayload);
                    List<CanSignalVo> canSignalVoList = new ArrayList<>();
                    List<Signal> signals = message.getSignals();
                    Map<String, DecodedSignal> decodeData;
                    decodeData = message.decodeByDecodedSignal(validPayload);
                    for (Signal signal : signals) {
                        Map.Entry<String, DecodedSignal> stringDecodedSignalEntry = decodeData.entrySet().stream().filter(o -> o.getKey().equals(signal.getName())).findFirst().get();
                        DecodedSignal decodedSignal = stringDecodedSignalEntry.getValue();
                        CanSignalVo canSignalVoR = new CanSignalVo(signal.getName(), String.valueOf(decodedSignal.getRawValue()), decodedSignal.getPhyValue(), signal.getNotes());
                        canSignalVoList.add(canSignalVoR);
                    }
                    canMessageVo.setName(message.getName());
                    canMessageVo.setCanSignalList(canSignalVoList);
                    canMessageVos.add(canMessageVo);
                } catch (DecodingFrameLengthException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return canMessageVos;
    }

    private List<CanMessageVo> fetchData(List<ZlgCanReturnData> list) {
        // 转换纳秒为秒
        List<CanMessageVo> canMessageVos = new ArrayList<>();
        for (ZlgCanReturnData zlgCanReturnData : list) {
            String id = String.format("0x%02X", zlgCanReturnData.getId());
            CanMessageVo canMessageVo = new CanMessageVo();
            canMessageVo.setTime(Double.parseDouble(String.format("%.6f", zlgCanReturnData.getTimestamp() / 1_000_000.0)));
            canMessageVo.setId(id);
            //canMessage.setChn(canR.getChn());
            canMessageVo.setEventType(zlgCanReturnData.getEventType());
            canMessageVo.setDir(zlgCanReturnData.getDir());
            canMessageVo.setDlc(zlgCanReturnData.getDlc());
            canMessageVo.setName("");
            byte[] validPayload = Arrays.copyOf(zlgCanReturnData.getData(), zlgCanReturnData.data.length);
            canMessageVo.setData(validPayload);
            canMessageVos.add(canMessageVo);
        }
        return canMessageVos;
    }

    //FIXME: 违背了接口依赖最小化原则
    @Override
    public String notificationUpgrade(int fileType) {
        return null;
    }

    @Override
    public boolean compareVersion(String ptsSwVersion) {
        return false;
    }

    @Override
    public double fetchXCPRX(String ecuNodeName, String xcpName) {
        return 0;
    }

    @Override
    public boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) {
        return false;
    }

    @Override
    public void send(FlexrayMessage message, Float timeout) throws BusError {
    }

    @Override
    public void startRealTimeData(Integer deviceChannel, CanMessageRealTimeSave canMessageRealTimeSave) {
        log.info("开启周立功{}通道{}日志实时保存", getDeviceName(), deviceChannel);
        log.info("保存路径:{}", canMessageRealTimeSave.getFilePath());
        canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.startRealTimeData();
    }

    @Override
    public void stopRealTimeData() {
        log.info("关闭周立功{}日志实时保存", getDeviceName());
        canLogService.stopRealTimeData();
        canLogService = null;
    }

    @Override
    public void startCaptureFrameCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter) {
        log.info("添加周立功{}通道{}日志(Frame)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}", canLogRealTimeSaveParameter.getFriendlyString());
        startFrameReceiver(deviceChannel);
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureFrameCanLog(Integer deviceChannel) {
        log.info("停止周立功{}日志(Frame)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopFrameReceiver(deviceChannel);
        } catch (Exception e) {
            log.error("关闭读取周立功{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }

    @Override
    public void startCaptureDbcCanLog(Integer deviceChannel, CanLogRealTimeSaveParameter canLogRealTimeSaveParameter) {
        log.info("添加周立功{}通道{}日志(DBC)实时保存", getDeviceName(), deviceChannel);
        log.info("实时抓取canLog的相关信息有:{}", canLogRealTimeSaveParameter.getFriendlyString());
        startDbcReceiver(deviceChannel, canLogRealTimeSaveParameter.getDbcConfig());
        canLogService = new CanLogServiceImpl(canLogRealTimeSaveParameter);
        canLogService.startCaptureCanLog();
    }

    @Override
    public void stopCaptureDbcCanLog(Integer deviceChannel) {
        log.info("停止周立功{}日志(DBC)实时保存", getDeviceName());
        canLogService.stopCaptureCanLog();
        try {
            stopDbcReceiver(deviceChannel);
        } catch (Exception e) {
            log.error("关闭读取周立功{}通道{}失败！", getDeviceName(), deviceChannel);
        }
        canLogService = null;
    }

    @Override
    public void saveLog(CanMessageRealTimeSave canMessageRealTimeSave) throws BlfException {
        log.info("保存周立功{}日志", getDeviceName());
        CanLogServiceImpl canLogService = new CanLogServiceImpl(canMessageRealTimeSave);
        canLogService.saveLog(fixedQueue);
    }

    /**
     * 读取数据
     *
     * @param deviceChannel
     * @param targetCanId
     * @param isCanFd
     * @param timeoutMilliseconds
     * @return
     */
    @Override
    public byte[] readDataByIdHex(int deviceChannel, int targetCanId, boolean isCanFd, long timeoutMilliseconds) {
        long timeout = isSimulated() ? 0 : timeoutMilliseconds;
        if (readZlgApi == null) {
            readZlgApi = new ZlgApi();
        }
        ZlgCanReturnData zlgCanReturnData = readZlgApi.readDataByIdHex(channelHandleMap.get(deviceChannel), deviceChannel, targetCanId, timeout, isCanFd);
        return zlgCanReturnData == null ? new byte[]{} : zlgCanReturnData.getData();
    }

    public void clearBuffer(int deviceChannel) {
        ZlgCanLib.Instance.ZCAN_ClearBuffer(channelHandleMap.get(deviceChannel));
    }


    @Override
    public long getLatestCanTimestamp(Integer deviceChannel) {
        if (rxBuffer != null && !rxBuffer.isEmpty()) {
            CanMessageVo latestMsg = rxBuffer.getFromTail(0);
            return (long) (latestMsg.getTime() * 1000); // 秒转毫秒
        }
        return 0;
    }

    @Override
    public Object getReceiveBuffer() {
        return rxBuffer;
    }

    @Override
    public Object readFromTail(Object buffer, int index) {
        return ((ZlgCircularBuffer<CanMessageVo>) buffer).getFromTail(index);
    }

    @Override
    public int getDataFrameId(Object data) {
        return Integer.parseInt(((CanMessageVo) data).getId().replace("0x", ""), 16);
    }

    @Override
    public long getTimestamp(Object data) {
        return (long) (((CanMessageVo) data).getTime() * 1000);
    }

    @Override
    public byte[] getDataBytes(Object data) {
        return ((CanMessageVo) data).getData();
    }

    @Override
    public boolean isBufferEmpty(Object buffer) {
        return ((ZlgCircularBuffer<CanMessageVo>) buffer).isEmpty();
    }

    @Override
    public int getBufferSize(Object buffer) {
        return ((ZlgCircularBuffer<CanMessageVo>) buffer).size();
    }
    @Override
    public void sendFlowControlFrame(CanMessage canMessage, byte[] flowControlFrame) throws BusError {
        canMessage.setData(flowControlFrame);
        canMessage.setSendTimes(1);
        send(canMessage);
    }

    @Override
    public boolean isValidFirstFrame(Object data) {
        return getDataBytes(data)[0] == 0x10;
    }

    @Override
    public boolean isValidFlowControlFrame(Object data, int receiveId, Object msgData) {
        return getDataBytes(data)[0] == 0x30 &&
                getDataFrameId(data) == receiveId &&
                getTimestamp(data) > getTimestamp(msgData);
    }

    @Override
    public boolean isContinueFrame(Object data) {
        return getDataBytes(data)[0] == 0x31;
    }

    @Override
    public boolean isAbortFrame(Object data) {
        return getDataBytes(data)[0] == 0x32;
    }
    @Override
    public String checkReplyData(Integer deviceChannel, String messageId, String byteData) {
        byte[] expectedData = buildExpectedData(byteData);
        return checkReplyDataCommonLogic(deviceChannel, messageId, expectedData, rxBuffer);
    }

    @Override
    public String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError{
        byte[] expectedData = buildExpectedData(expectResult);
        return checkReplyDataCommonLogic(deviceChannel, "", expectedData, rxBuffer);
    }

    private byte[] buildExpectedData(String byteData) {
        byte[] expectedData = ByteUtils.hexStringToByteArray(byteData);
        byte[] newData = new byte[expectedData.length + 1];
        newData[0] = (byte) expectedData.length;
        System.arraycopy(expectedData, 0, newData, 1, expectedData.length);
        return newData;
    }

    /**
     * 从数据对象中获取通道号
     *
     * @param data 数据对象（通常是 CanMessageVo）
     * @return 通道号
     */
    @Override
    public int getChannelFromData(Object data) {
        if (data instanceof CanMessageVo) {
            return Integer.parseInt(((CanMessageVo) data).getChn());
        }
        throw new IllegalArgumentException("Unsupported data type for getChannelFromData: " + data.getClass().getName());
    }


    public static void main(String[] args) {

    }
}
