package com.desaysv.workserver.devices.qnx;

import com.desaysv.workserver.base.operation.invoker.OperationResult;
import com.desaysv.workserver.base.operation.method.OperationFailNotification;
import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.serial.SerialPortDevice;
import com.desaysv.workserver.devices.serial.SerialUtils;
import com.desaysv.workserver.devices.serial.listener.SerialPortOpenListener;
import com.desaysv.workserver.devices.serial.manager.SerialPortEventManager;
import com.desaysv.workserver.entity.TemplateImageConfig;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.exceptions.device.DeviceOpenRepeatException;
import com.desaysv.workserver.exceptions.device.DeviceSendException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;
import com.desaysv.workserver.utils.command.CmdCommand;
import com.desaysv.workserver.utils.command.CommandExecutor;
import com.desaysv.workserver.utils.command.CommandResponse;
import com.desaysv.workserver.utils.command.CommandUtils;
import com.fazecast.jSerialComm.SerialPort;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
public class QnxInstrument extends QnxDevice implements SerialPortOpenListener {

    private SerialPortDevice externalSerialPortDevice; // 外部串口设备
    private transient SerialPortEventManager serialPortEventManager; // 串口事件管理器
    private boolean isListenerRegistered = false; // 监听器是否已注册
    private String commandStr;
    private RectSize rectSize; // 假定此 rectSize 是固定的，或在首次成功抓取时设置一次

    // 成员变量用于 FFmpegFrameGrabber 管理
    private transient FFmpegFrameGrabber frameGrabberInstance = null;
    private String activeRtmpUrl = null; // 存储首次成功初始化时使用的固定RTMP URL
    private final Object grabberLock = new Object();

    public QnxInstrument() {
        this(new DeviceOperationParameter());
    }

    public QnxInstrument(DeviceOperationParameter deviceOperationParameter) {
        super(deviceOperationParameter);
        try {
            this.serialPortEventManager = SerialPortEventManager.getInstance();
            if (this.serialPortEventManager != null && !this.isListenerRegistered) {
                this.serialPortEventManager.registerListener(this);
                this.isListenerRegistered = true;
                log.debug("QnxInstrument '{}' 已注册到 SerialPortOpenListener。", getDeviceName());
            }
        } catch (Exception e) {
            log.error("QnxInstrument '{}': 获取 SerialPortEventManager 实例失败。可能无法发布串口打开/关闭事件。", getDeviceName(), e);
            this.serialPortEventManager = null;
        }
    }

    /**
     * 设置用于通信的外部 SerialPortDevice。
     *
     * @param device 要使用的 SerialPortDevice。
     */
    public void setExternalSerialPortDevice(SerialPortDevice device) {
        if (device != null) {
            log.info("QnxInstrument '{}': 设置外部 SerialPortDevice 为 '{}' (端口: {}).",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
        } else if (this.externalSerialPortDevice != null) {
            log.info("QnxInstrument '{}': 清除外部 SerialPortDevice (原为 '{}').",
                    getDeviceName(), this.externalSerialPortDevice.getAliasName());
        }
        this.externalSerialPortDevice = device;
    }

    @Override
    public String getDeviceType() {
        return DeviceType.DEVICE_QNX;
    }

    @Override
    public String getDeviceModel() {
        return DeviceModel.Qnx.QNX_INSTRUMENT;
    }

    @Override
    public boolean open() throws DeviceOpenException, DeviceOpenRepeatException {

        // 根据实际的打开逻辑返回true或false
        // 此处仅为示例，保持原有逻辑
        return true;
    }

    @Override
    public boolean close() throws DeviceCloseException {
        log.info("QnxInstrument '{}': close() 调用.", getDeviceName());
        synchronized (grabberLock) {
            if (frameGrabberInstance != null) {
                try {
                    log.info("QnxInstrument '{}': 正在停止并释放 FFmpegFrameGrabber (URL: {}).", getDeviceName(), activeRtmpUrl);
                    frameGrabberInstance.stop();
                    frameGrabberInstance.release();
                } catch (FFmpegFrameGrabber.Exception e) {
                    log.error("QnxInstrument '{}': 关闭期间停止/释放 FFmpegFrameGrabber 出错: {}", getDeviceName(), e.getMessage(), e);
                }
                frameGrabberInstance = null;
                activeRtmpUrl = null;
            }
        }

        if (this.serialPortEventManager != null && this.isListenerRegistered) {
            this.serialPortEventManager.unregisterListener(this);
            this.isListenerRegistered = false;
            log.debug("QnxInstrument '{}' 已从 SerialPortOpenListener 注销.", getDeviceName());
        }
        return true;
    }

    @Override
    public void onSerialPortOpened(SerialPortDevice device) {
        if (device == null) return;
        log.info("QnxInstrument '{}' 收到设备 '{}' (端口: {}) 的 onSerialPortOpened 事件。",
                getDeviceName(), device.getAliasName(), device.getDevicePortName());

        // 检查打开的串口是否是 QnxInstrument 自身配置的串口
        if (this.externalSerialPortDevice == null) {
            log.info("QnxInstrument '{}': 匹配的 SerialPortDevice '{}' (端口: {}) 已打开。设置为外部设备。",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
            setExternalSerialPortDevice(device);
        } else if (this.externalSerialPortDevice == device) {
            // 如果事件对应的设备已经是当前外部设备，则无需操作
            log.debug("QnxInstrument '{}': 收到已设置的外部设备 '{}' 的打开事件。无变化。", getDeviceName(), device.getAliasName());
        } else {
            // 如果一个匹配的串口设备打开了，但是已经设置了另一个外部设备，则记录警告
            log.warn("QnxInstrument '{}': 一个匹配的 SerialPortDevice '{}' 已打开, 但已设置外部设备 '{}'。不进行更改。",
                    getDeviceName(), device.getAliasName(), this.externalSerialPortDevice.getAliasName());
        }
    }

    @Override
    public void onSerialPortClosed(SerialPortDevice device) {
        if (device == null) return;
        log.debug("QnxInstrument '{}' 收到设备 '{}' (端口: {}) 的 onSerialPortClosed 事件。",
                getDeviceName(), device.getAliasName(), device.getDevicePortName());
        // 检查关闭的串口是否是当前使用的外部串口设备
        if (this.externalSerialPortDevice == device) {
            log.info("QnxInstrument '{}': 外部 SerialPortDevice '{}' (端口: {}) 已关闭。清除引用。",
                    getDeviceName(), device.getAliasName(), device.getDevicePortName());
            setExternalSerialPortDevice(null); // 清除外部串口设备引用
        }
    }

    /**
     * 向串口发送QNX设备初始化命令。
     * 此方法优先使用已设置的 externalSerialPortDevice (如果它已打开)。
     * 否则，它将使用QnxDevice自身的配置通过jSerialComm打开新端口来发送命令。
     *
     * @return 是否发送成功
     */
    public boolean sendQnxInitCommands() {
        String[] commands = {
                "pfctl -d",
                "dtach -a /tmp/android",
                "su",
                "echo peripheral > /sys/devices/platform/soc/a600000.ssusb/mode",
                "su",
                "setprop persist.sv.debug.adb_enable 1",
                "su",
                "setprop persist.sv.enable_adb_install 1",
                "iptables -P INPUT ACCEPT",
                "iptables -P OUTPUT ACCEPT",
                "iptables -P FORWARD ACCEPT",
                "iptables -F",
        };

        if (this.externalSerialPortDevice != null && this.externalSerialPortDevice.getSerialPort() != null && this.externalSerialPortDevice.getSerialPort().isOpen()) {
            try {
                log.info("使用已连接的串口设备 ({}) 发送QNX初始化命令", this.externalSerialPortDevice.getDevicePortName());
                for (String cmd : commands) {
                    log.info("发送命令: {}", cmd);
                    this.externalSerialPortDevice.send((cmd + "\n").getBytes(), false);
                    Thread.sleep(100); // 等待命令执行
                }
                log.info("QNX初始化命令发送完成");
                return true;
            } catch (DeviceSendException e) {
                log.error("通过已连接的串口设备发送QNX初始化命令失败: {}", e.getMessage(), e);
                return false;
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                log.error("命令发送被中断", e);
                return false;
            }
        } else {
            // 回退到使用内部串口逻辑
            String serialPortName = getDevicePortName(); // QnxDevice自身配置的串口名
            Integer baudRate = getBaudRate(); // QnxDevice自身配置的波特率
            int currentBaudRate = (baudRate != null) ? baudRate : 115200; // 默认波特率

            if (serialPortName == null || serialPortName.isEmpty()) {
                log.warn("未配置串口名称 (QnxDevice)，无法发送QNX初始化命令");
                return false;
            }

            SerialPort internalSerialPort = null;
            try {
                log.info("正在向串口 {} @ {} 波特率发送QNX初始化命令 (内部回退逻辑)", serialPortName, currentBaudRate);
                internalSerialPort = SerialPort.getCommPort(serialPortName);
                if (!internalSerialPort.isOpen()) {
                    internalSerialPort.setComPortParameters(currentBaudRate, 8, SerialPort.ONE_STOP_BIT, SerialPort.NO_PARITY);
                    internalSerialPort.setComPortTimeouts(SerialPort.TIMEOUT_WRITE_BLOCKING, 0, 5000); // 设置超时
                    if (!internalSerialPort.openPort()) {
                        log.error("无法打开串口: {}", serialPortName);
                        return false;
                    }
                }

                for (String cmd : commands) {
                    log.info("发送QNX命令 (内部): {}", cmd);
                    SerialUtils.sendToPort(internalSerialPort, cmd + "\n");
                    Thread.sleep(200); // 等待命令执行
                }
                log.info("QNX初始化命令发送完成 (内部回退逻辑)");
                return true;
            } catch (Exception e) { // 捕获更广泛的异常，例如打开串口失败
                log.error("发送QNX初始化命令失败 (内部回退逻辑): {}", e.getMessage(), e);
                if (e instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
                return false;
            } finally {
                if (internalSerialPort != null && internalSerialPort.isOpen()) {
                    internalSerialPort.closePort(); // 确保串口关闭
                }
            }
        }
    }

    /**
     * 获取QNX视频流
     *
     * @param filePath 文件路径
     */
    public void startStream(String filePath) {
        startStream(filePath, false);
    }

    /**
     * 停止QNX视频流
     */
    public void stopStream() {
        CommandExecutor.killByCommand(commandStr);
    }

    /**
     * 执行QNX命令文件
     *
     * @param filePath 文件路径
     */
    public void startStream(String filePath, boolean useSystemCmd) {
        // 首先尝试发送初始化命令
        if (sendQnxInitCommands()) {
            log.info("QNX初始化命令发送成功。");
        } else {
            log.warn("QNX初始化命令发送失败，可能影响后续QNX命令文件执行");
        }

        log.info("准备执行QNX命令文件，路径: {}", filePath);
        if (useSystemCmd) {
            useSystemCmd(filePath);
        } else {
            useCommandUtils(filePath);
        }

    }

    /**
     * 使用CommandUtils执行QNX命令文件
     *
     * @param filePath 文件路径
     */
    private void useCommandUtils(String filePath) {
        try {
            File scriptFile = new File(filePath);
            String scriptDir = scriptFile.getParent();

            if (scriptDir == null) {
                try {
                    // If filePath does not contain path information (e.g., just a filename),
                    // scriptFile.getParent() might return null.
                    // Try to get the current working directory as a fallback.
                    scriptDir = new File(".").getCanonicalPath();
                    log.warn("无法从 '{}' 确定脚本的父目录，将使用备选工作目录 '{}'", filePath, scriptDir);
                } catch (IOException e) {
                    log.error("无法获取备选工作目录，执行可能失败。", e);
                    // Optionally re-throw or set a default known safe path
                    // For now, we'll let it proceed, but executionDirectory might be problematic.
                    // Or, better, return or throw:
                    throw new RuntimeException("无法确定脚本执行目录", e);
                }
            }

            // Command to start a new command prompt window, set its title, working directory,
            // and execute the script, keeping the window open.
            commandStr = filePath;

            CmdCommand cmdCommand = new CmdCommand(commandStr);
            // setShowWindow(true) is important if CommandExecutor needs a hint to allow 'start' to create a new window.
            // The 'start' command itself is responsible for the new window.
            cmdCommand.setShowWindow(true);
            cmdCommand.setWorkDir(getWorkDir(filePath));

            ExecutorService executor = Executors.newSingleThreadExecutor();
            final String finalScriptDir = scriptDir; // Effective final for use in lambda

            executor.submit(() -> {
                try {
                    log.info("准备通过 CommandUtils.executeCommand(CmdCommand) 在新窗口执行命令 (窗口将保持打开): {}", cmdCommand.getCommand());
                    log.info("脚本路径: {}, 脚本将在目录 '{}' 中执行 (通过 /D 参数)", filePath, finalScriptDir);

                    // Execute the command
                    CommandResponse response = CommandUtils.executeCommand(cmdCommand);

                    // Log the response from the 'start' command execution itself.
                    // The actual script's output will be in the new window.
                    log.info("CommandUtils.executeCommand(CmdCommand) 执行完成. Success: {}, Output: {}, Error: {}",
                            response.isOk(), response.getStandardOutput(), response.getStandardError());

                } catch (IOException e) {
                    log.error("使用 CommandUtils.executeCommand(CmdCommand) 执行QNX命令文件 '{}' (命令: {}) 时发生IOException: {}",
                            filePath, cmdCommand.getCommand(), e.getMessage(), e);
                } catch (Exception e) {
                    log.error("执行QNX命令文件 '{}' (命令: {}) 期间发生意外错误 (useCommandUtils lambda): {}",
                            filePath, cmdCommand.getCommand(), e.getMessage(), e);
                }
            });
            executor.shutdown();

        } catch (Exception e) {
            // Catch exceptions from the setup phase (file operations, command string formatting)
            log.error("准备执行QNX命令文件 '{}' (useCommandUtils) 时发生顶层异常: {}", filePath, e.getMessage(), e);
        }
    }

    /**
     * 获取脚本目录
     *
     * @param filePath 脚本路径
     * @return
     */
    private String getWorkDir(String filePath) {
        File scriptFile = new File(filePath);
        String scriptDir = scriptFile.getParent();

        if (scriptDir == null) {
            try {
                // 如果 filePath 不包含路径信息 (例如，只是一个文件名),
                // 则 scriptFile.getParent() 可能返回 null。
                // 尝试获取当前工作目录作为备选。
                scriptDir = new File(".").getCanonicalPath();
                log.warn("无法从 '{}' 确定脚本的父目录，将使用备选工作目录 '{}'", filePath, scriptDir);
            } catch (IOException e) {
                log.error("无法获取备选工作目录，执行可能失败。", e);
                // 可以选择抛出异常或设置一个默认的已知安全路径
                throw new RuntimeException("无法确定脚本执行目录", e);
            }
        }
        return scriptDir;
    }

    /**
     * 使用系统命令执行QNX命令文件
     *
     * @param filePath 文件路径
     */
    private void useSystemCmd(String filePath) {
        try {
            String scriptDir = getWorkDir(filePath);

            // 此命令将由 CommandUtils.executeCommandToString 执行 (假设其内部会添加 "cmd /c")
            // 1. 'start': 启动一个新命令提示符窗口。
            // 2. "Executing [脚本文件名]": 设置新窗口的标题。
            // 3. '/D "[脚本目录]"': 为新窗口中的命令设置工作目录。
            // 4. 'cmd /k "[脚本文件路径]"': 在新窗口中执行脚本，'/k' 参数会使窗口在脚本执行后保持打开状态，便于查看输出或错误。
            String commandForCommandUtils = String.format("start \"Executing %s\" /D \"%s\" cmd /k \"%s\"",
                    new File(filePath).getName(),
                    scriptDir,
                    filePath);

            ExecutorService executor = Executors.newSingleThreadExecutor();
            // CommandUtils 需要一个 File 对象作为工作目录参数
            final File executionDirectory = new File(scriptDir);

            executor.submit(() -> {
                try {
                    log.info("准备通过 CommandUtils 在新窗口执行命令 (窗口将保持打开): {}", commandForCommandUtils);
                    log.info("脚本路径: {}, 脚本将在目录 '{}' 中执行", filePath, executionDirectory.getAbsolutePath());

                    // 假设 CommandUtils.executeCommandToString(cmd, dir) 内部会执行 "cmd /c cmd"
                    // 其中 cmd 是 commandForCommandUtils，dir 是 executionDirectory
                    String result = CommandUtils.executeCommandToString(commandForCommandUtils, executionDirectory);

                    log.info("命令 'start' 已尝试执行。 'start' 命令本身的输出 (如果存在): {}", result);
                    // 脚本的实际输出/错误将显示在弹出的新窗口中。
                } catch (IOException e) {
                    log.error("使用 CommandUtils.executeCommandToString 执行QNX命令文件 '{}' (命令: {}) 时发生IOException: {}", filePath, commandForCommandUtils, e.getMessage(), e);
                } catch (Exception e) {
                    log.error("执行QNX命令文件 '{}' (命令: {}) 期间发生意外错误: {}", filePath, commandForCommandUtils, e.getMessage(), e);
                }
            });
            executor.shutdown();
        } catch (Exception e) {
            // 捕获准备阶段的顶层异常，例如 scriptDir 解析失败时的 RuntimeException
            log.error("准备执行QNX命令文件 '{}' 时发生顶层异常: {}", filePath, e.getMessage(), e);
        }
    }


    @Override
    public OperationResult testSimilarity(TemplateImageConfig templateImageConfig) throws OperationFailNotification {
        if (isSimulated()) {
            log.info("设备模拟中，相似度测试不启用");
            return OperationResult.staticOk();
        }
        return super.testSimilarity(templateImageConfig);
    }

    @Override
    public Frame grabFrame() {
        synchronized (grabberLock) {
            // 阶段1: 确保 FrameGrabber 已根据固定配置初始化
            if (frameGrabberInstance == null) {
                if (this.activeRtmpUrl == null) { // 尚未获取过固定的RTMP URL
                    String rtmpUrlFromConfig = getDeviceName();
                    if (rtmpUrlFromConfig == null || rtmpUrlFromConfig.isEmpty()) {
                        log.error("QnxInstrument '{}': 固定 RTMP URL 未配置或不可用。无法初始化 grabber。", getDeviceName());
                        return createFallbackFrame("RTMP URL 未配置");
                    }
                    this.activeRtmpUrl = rtmpUrlFromConfig; // 存储固定的URL
                }

                // 执行首次/必要的初始化
                if (!initializeAndStartGrabber(this.activeRtmpUrl, "首次初始化")) {
                    return createFallbackFrame("Grabber 初始化失败");
                }
            }

            // 阶段 2: 尝试抓取帧
            try {
                Frame frame = frameGrabberInstance.grab();
                if (frame == null) {
                    log.warn("QnxInstrument '{}': frameGrabberInstance.grab() (URL: {}) 返回了 null。尝试重连...", getDeviceName(), this.activeRtmpUrl);
                    // grab()返回null，也视为连接问题，尝试重连
                    return handleGrabFailureWithReconnect("grab() 返回 null");
                }
                validateFrameSize(frame);
                return frame;
            } catch (FFmpegFrameGrabber.Exception e) {
                log.warn("QnxInstrument '{}': 从 FFmpegFrameGrabber (URL: {}) 抓取帧时发生异常: {}。尝试重连...", getDeviceName(), this.activeRtmpUrl, e.getMessage());
                return handleGrabFailureWithReconnect(e.getMessage());
            }
        }
    }

    private boolean initializeAndStartGrabber(String rtmpUrl, String contextLog) {
        log.info("QnxInstrument '{}': {} FFmpegFrameGrabber (URL: {})...", getDeviceName(), contextLog, rtmpUrl);
        try {
            if (frameGrabberInstance != null) { // 如果因重连调用，先清理旧的
                try {
                    frameGrabberInstance.stop();
                    frameGrabberInstance.release();
                } catch (FFmpegFrameGrabber.Exception e) {
                    log.warn("QnxInstrument '{}': 在 {} 期间清理旧 grabber 实例时出错: {}", getDeviceName(), contextLog, e.getMessage());
                }
            }
            frameGrabberInstance = new FFmpegFrameGrabber(rtmpUrl);
            frameGrabberInstance.setOption("stimeout", "5000000"); // 5 秒连接超时 (微秒)
            frameGrabberInstance.start();
            // activeRtmpUrl 已经在调用此方法前设置或确认
            log.info("QnxInstrument '{}': FFmpegFrameGrabber ({}) 已成功启动。Grabber报告尺寸: {}x{}", getDeviceName(), contextLog, frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight());

            // 如果 this.rectSize 尚未设定 (仅在非常首次的成功启动时)
            if (this.rectSize == null) {
                if (frameGrabberInstance.getImageWidth() > 0 && frameGrabberInstance.getImageHeight() > 0) {
                    this.rectSize = new RectSize(frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight());
                    log.info("QnxInstrument '{}': rectSize 从 grabber ({}) 初始化为: {}x{}", getDeviceName(), contextLog, this.rectSize.getWidth(), this.rectSize.getHeight());
                } else {
                    log.error("QnxInstrument '{}': Grabber ({}) 启动但未提供有效尺寸，且 rectSize 未预设。初始化失败。", getDeviceName(), contextLog);
                    cleanUpGrabberAfterFailure();
                    return false;
                }
            } else {
                // rectSize 已预设，检查是否与 grabber 报告的尺寸一致 (仅日志)
                if (frameGrabberInstance.getImageWidth() > 0 && frameGrabberInstance.getImageHeight() > 0 &&
                        (frameGrabberInstance.getImageWidth() != this.rectSize.getWidth() ||
                                frameGrabberInstance.getImageHeight() != this.rectSize.getHeight())) {
                    log.warn("QnxInstrument '{}': Grabber ({}) 报告的图像尺寸 {}x{} 与预设的 rectSize {}x{} 不符。将优先使用预设的 rectSize。",
                            getDeviceName(), contextLog, frameGrabberInstance.getImageWidth(), frameGrabberInstance.getImageHeight(),
                            this.rectSize.getWidth(), this.rectSize.getHeight());
                }
            }
            return true;
        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("QnxInstrument '{}': {} FFmpegFrameGrabber (URL: {}) 失败: {}", getDeviceName(), contextLog, rtmpUrl, e.getMessage(), e);
            cleanUpGrabberAfterFailure();
            return false;
        }
    }

    private Frame handleGrabFailureWithReconnect(String failureReason) {
        log.info("QnxInstrument '{}': 处理抓取失败 ({})，尝试重连并重新抓取...", getDeviceName(), failureReason);
        // 清理当前可能已损坏的grabber实例，activeRtmpUrl保留，因为它是固定的
        if (frameGrabberInstance != null) {
            try {
                frameGrabberInstance.stop();
                frameGrabberInstance.release();
            } catch (FFmpegFrameGrabber.Exception e) {
                log.warn("QnxInstrument '{}': 清理失败的grabber时出错: {}", getDeviceName(), e.getMessage());
            }
            frameGrabberInstance = null; // 确保下次 initializeAndStartGrabber 会创建新实例
        }

        // 尝试重新初始化并启动
        if (initializeAndStartGrabber(this.activeRtmpUrl, "重连后初始化")) {
            try {
                log.info("QnxInstrument '{}': 重连成功，尝试再次抓取帧...", getDeviceName());
                Frame frame = frameGrabberInstance.grab();
                if (frame == null) {
                    log.warn("QnxInstrument '{}': 重连后 grab() 仍返回 null (URL: {})。", getDeviceName(), this.activeRtmpUrl);
                    return createFallbackFrame("重连后 grab() 返回 null");
                }
                validateFrameSize(frame);
                return frame;
            } catch (FFmpegFrameGrabber.Exception e) {
                log.error("QnxInstrument '{}': 重连后抓取帧仍失败 (URL: {}): {}", getDeviceName(), this.activeRtmpUrl, e.getMessage(), e);
                cleanUpGrabberAfterFailure(); // 清理重连尝试中创建的grabber
                return createFallbackFrame("重连后 grab() 失败");
            }
        } else {
            log.error("QnxInstrument '{}': 重连尝试中的 Grabber 初始化失败 (URL: {})。", getDeviceName(), this.activeRtmpUrl);
            return createFallbackFrame("重连初始化失败");
        }
    }

    private void cleanUpGrabberAfterFailure() {
        if (frameGrabberInstance != null) {
            try {
                frameGrabberInstance.release(); // stop可能已在initializeAndStartGrabber中调用或失败时调用
            } catch (FFmpegFrameGrabber.Exception e) {
                log.warn("QnxInstrument '{}': 清理grabber (release) 时出错: {}", getDeviceName(), e.getMessage());
            }
            frameGrabberInstance = null;
        }
        // activeRtmpUrl 不清除，因为它是固定配置
    }

    private Frame createFallbackFrame(String reason) {
        log.warn("QnxInstrument '{}': 由于 {}，无法提供真实帧。", getDeviceName(), reason);
        if (this.rectSize != null) {
            log.warn("QnxInstrument '{}': 返回基于预设/已确定 rectSize {}x{} 的空白帧。", getDeviceName(), this.rectSize.getWidth(), this.rectSize.getHeight());
            return new Frame(this.rectSize.getWidth(), this.rectSize.getHeight(), Frame.DEPTH_UBYTE, 3);
        }
        log.error("QnxInstrument '{}': rectSize 未设置，无法创建回退帧，返回 null。", getDeviceName());
        return null;
    }

    private void validateFrameSize(Frame frame) {
        if (this.rectSize != null && frame != null &&
                (frame.imageWidth != this.rectSize.getWidth() || frame.imageHeight != this.rectSize.getHeight())) {
            log.warn("QnxInstrument '{}': 抓取到的帧尺寸 {}x{} 与已建立的 rectSize {}x{} 不符! 图像内容可能不符合预期。",
                    getDeviceName(), frame.imageWidth, frame.imageHeight,
                    this.rectSize.getWidth(), this.rectSize.getHeight());
        }
    }

    @Override
    public RectSize getSize() {
        if (rectSize == null) {
            try (Frame frame = grabFrame()) {
                rectSize = new RectSize(frame.imageWidth, frame.imageHeight);
            }
        }
        return rectSize;
    }
}
