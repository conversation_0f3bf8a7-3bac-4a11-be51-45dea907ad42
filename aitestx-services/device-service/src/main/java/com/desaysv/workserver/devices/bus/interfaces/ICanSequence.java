package com.desaysv.workserver.devices.bus.interfaces;

import cantools.dbc.DecodedSignal;
import cn.hutool.core.util.RandomUtil;
import com.desaysv.workserver.action_sequence.*;
import com.desaysv.workserver.annotation.RegexRule;
import com.desaysv.workserver.config.can.UdsModel;
import com.desaysv.workserver.devices.bus.base.BusError;
import com.desaysv.workserver.exceptions.SimulatedDeviceNotification;
import com.desaysv.workserver.exceptions.device.DeviceCloseException;
import com.desaysv.workserver.exceptions.device.DeviceOpenException;
import com.desaysv.workserver.text.ActionSequencesLoggerUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;
import java.util.concurrent.*;

import static com.desaysv.workserver.devices.bus.canoe.VectorUtils.waitTime;
import static com.desaysv.workserver.devices.bus.fdx.FdxUtils.doubleToHexString;
import static com.desaysv.workserver.utils.ByteUtils.additionHex;
import static com.desaysv.workserver.utils.ByteUtils.subtractionHex;
import static com.desaysv.workserver.utils.StrUtils.*;

/**
 * CAN动作序列实现类
 */
public interface ICanSequence extends IControllableAction, IBusDevice {
    Logger log = LogManager.getLogger(ICanSequence.class.getSimpleName());
    // 类级别定义线程池和结果容器
    ExecutorService stepExecutor = Executors.newCachedThreadPool();
    ConcurrentMap<String, ActualExpectedResult> stepResults = new ConcurrentHashMap<>();
    Map<String, Future<?>> taskFutures = new ConcurrentHashMap<>();

    int RETRY_TIMES = 1;
    int RETRY_INTERVAL = 1000;

    default String optimizeByteString(String byteString) {
        return byteString.replaceAll("\\s+", "");
    }

    default String optimizeHexString(String hexString) {
        return hexString.replace("0x", "");
    }

    /**
     * 是否失败重试
     *
     * @return
     */
    default boolean isFailReTry() {
        return false;
    }

    /**
     * 获取Can消息周期
     *
     * @param deviceChannel
     * @param messageName
     * @return 毫秒周期
     */
    int getCanMessageCycle(Integer deviceChannel, String messageName);


    /**
     * 设置Can信号值（原始值）
     *
     * @param deviceChannel  Can通道
     * @param ecuNodeName    ECU节点名称
     * @param signalName     信号名称
     * @param signalRawValue 信号值（原始值）
     * @return 是否设置Can信号成功
     */
    boolean setCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, long signalRawValue) throws BusError;

    /**
     * 设置Can信号值（物理值）
     *
     * @param deviceChannel  Can通道
     * @param ecuNodeName    ECU节点名称
     * @param signalName     信号名称
     * @param signalPhyValue 信号值（物理值）
     * @return 是否设置Can信号成功
     */
    boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue) throws BusError;

    boolean setCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, double signalPhyValue, Integer cycle) throws BusError;

    //设置Can信号值（物理值或者0x实际值），有ECU
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).CHANGE_CAN_SIGNAL"})
    default ActualExpectedResult changeCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        //判断signalValueString是否是16进制0x开头的字符串,是就转成double类型
        boolean isHex = signalValueString.matches("^0x[0-9a-fA-F]+$");
        long signalValue = isHex ? Long.decode(signalValueString) : (long) removeParenthesesToDouble(signalValueString);
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = isHex ? setCanSignalRawValue(deviceChannel, ecuNodeName, messageName, signalName, signalValue) : setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, signalValue);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("changeCanSignal", pass, signalValueString);
        return actualExpectedResult;
    }

    //设置Can信号值（物理值或者0x实际值），无ECU
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).CHANGE_CAN_SIGNAL_WITHOUT_ECU"})
    default ActualExpectedResult changeCanSignal(Integer deviceChannel, String messageName, String signalName, String signalValueString) {
        return changeCanSignal(deviceChannel, null, messageName, signalName, signalValueString);
    }

    /**
     * 设置Can信号随机值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param lowerSignalPhyValue 信号值下限
     * @param upperSignalPhyValue 信号值上限
     * @return 是否设置Can信号成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).RANDOM_CAN_SIGNAL"})
    default ActualExpectedResult setRandomCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String lowerSignalPhyValue, String upperSignalPhyValue) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        double lowerSignal = removeParenthesesToDouble(lowerSignalPhyValue);
        double upperSignal = removeParenthesesToDouble(upperSignalPhyValue);
        double signalValue = RandomUtil.randomDouble(lowerSignal, upperSignal);
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, signalValue);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
            signalValue = Double.NaN;
        }
        actualExpectedResult.put("setRandomCanSignal", pass, signalValue);
        return actualExpectedResult;
    }

    //设置Can信号随机值（物理值），无ECU
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).RANDOM_CAN_SIGNAL_WITHOUT_ECU"})
    default ActualExpectedResult setRandomCanSignal(Integer deviceChannel, String messageName, String signalName, String lowerSignalPhyValue, String upperSignalPhyValue) {
        return setRandomCanSignal(deviceChannel, null, messageName, signalName, lowerSignalPhyValue, upperSignalPhyValue);
    }

    /**
     * 设置Can信号步进值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param startSignalPhyValue 起始信号值
     * @param endSignalPhyValue   终止信号值
     * @param step                信号值步进值
     * @return 是否设置Can信号步进值成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL_WITHOUT_TIME"})
    default ActualExpectedResult setStepCanSignalWithoutTime(Integer deviceChannel, String ecuNodeName, String messageName, String signalName,
                                                             Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step) {
        //获取dbc报文周期
        int messageCycle = getCanMessageCycle(deviceChannel, messageName);
        return setStepCanSignal(deviceChannel, ecuNodeName, messageName, signalName, startSignalPhyValue, endSignalPhyValue, step, messageCycle + "ms");
    }

    /**
     * 设置循环Can信号步进值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param startSignalPhyValue 起始信号值
     * @param endSignalPhyValue   终止信号值
     * @param step                信号值步进值
     * @param stepInterval        信号值步进间隔
     * @return 是否设置Can信号步进值成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL_CYCLE"})
    default ActualExpectedResult setStepCanSignalCycle(Integer deviceChannel, String ecuNodeName, String messageName, String signalName,
                                                       Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step, String stepInterval) {
        // 同一个通道的同一个ECU节点的同一个报文的同一个信号，则只执行一次步进改变
        String taskId = deviceChannel + ":" + ecuNodeName + ":" + messageName + ":" + signalName;

        log.info("循环步进信号值，从起始值 {} 到结束值 {}，步长为 {}，间隔时间为 {}，信号名称为 {}，消息名称为 {}，设备通道为 {}，ECU节点名称为 {}",
                startSignalPhyValue, endSignalPhyValue, step, stepInterval, signalName, messageName, deviceChannel, ecuNodeName);
        //先验证是否已经存在相同任务，如果存在则取消之前的任务
        Future<?> future = taskFutures.get(taskId);
        if (future != null) {
            future.cancel(true); // 中断特定任务
            stepResults.remove(taskId);
            taskFutures.remove(taskId);
        }
        // 创建结果容器（线程安全）
        ActualExpectedResult result = new AtomicActualExpectedResult();
        stepResults.put(taskId, result);

        // 提交异步任务
        Future<?> newFuture = stepExecutor.submit(() -> {
            try {
                executeCycleStepLogic(taskId, deviceChannel, ecuNodeName, messageName, signalName,
                        startSignalPhyValue, endSignalPhyValue, step, stepInterval);
                result.markComplete(true);
            } catch (Exception e) {
                result.markComplete(false);
            } finally {
                stepResults.remove(taskId);
                taskFutures.remove(taskId); // 清理 Future
            }
        });

        taskFutures.put(taskId, newFuture);

        ActualExpectedResult returnResult = new ActualExpectedResult();
        returnResult.put("setStepCycleCanSignal", true, "");
        return returnResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL_CYCLE_WITHOUT_ECU"})
    default ActualExpectedResult setStepCanSignalCycleWithoutECU(Integer deviceChannel, String messageName, String signalName,
                                                                 Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step, String stepInterval) {
        return setStepCanSignalCycle(deviceChannel, null, messageName, signalName, startSignalPhyValue, endSignalPhyValue, step, stepInterval);
    }

    /**
     * 设置物理（十进制）或者原始值（0x十六进制）
     *
     * @param value
     */
    default void setPhyOrRawValue(String value) {
    }

    void executeCycleStepLogic(String taskId, Integer deviceChannel, String ecuNodeName,
                               String messageName, String signalName, Integer start,
                               Integer end, int step, String interval);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STOP_STEP_CAN_SIGNAL_CYCLE"})
    default ActualExpectedResult stopStepCycleCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) {
        log.info("停止循环步进信号值，信号名称为 {}，消息名称为 {}，设备通道为 {}，ECU节点名称为 {}", signalName, messageName, deviceChannel, ecuNodeName);
        String taskId = deviceChannel + ":" + ecuNodeName + ":" + messageName + ":" + signalName;
        Future<?> future = taskFutures.get(taskId);
        if (future != null) {
            future.cancel(true); // 中断特定任务
            stepResults.remove(taskId);
            taskFutures.remove(taskId);
        }
        ActualExpectedResult result = new ActualExpectedResult();
        result.put("stopStepCanSignal", true, "");
        return result;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STOP_STEP_CAN_SIGNAL_CYCLE_WITHOUT_ECU"})
    default ActualExpectedResult stopStepCycleCanSignalWithoutECU(Integer deviceChannel, String messageName, String signalName) {
        return stopStepCycleCanSignal(deviceChannel, null, messageName, signalName);
    }

    /**
     * 设置Can信号步进值（物理值）,有ECU
     *
     * @param deviceChannel       Can通道
     * @param ecuNodeName         ECU节点名称
     * @param signalName          信号名称
     * @param startSignalPhyValue 起始信号值
     * @param endSignalPhyValue   终止信号值
     * @param step                信号值步进值
     * @param stepInterval        信号值步进间隔
     * @return 是否设置Can信号步进值成功
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL"})
    default ActualExpectedResult setStepCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName,
                                                  Integer startSignalPhyValue, Integer endSignalPhyValue, Integer step, String stepInterval) {
        log.info("步进信号值，从起始值 {} 到结束值 {}，步长为 {}，间隔时间为 {}，信号名称为 {}，消息名称为 {}，设备通道为 {}，ECU节点名称为 {}",
                startSignalPhyValue, endSignalPhyValue, step, stepInterval, signalName, messageName, deviceChannel, ecuNodeName);
        final long milliseconds = (long) (BaseRegexRule.getSecondsOfDefaultMills(stepInterval) * 1000);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        double signalValue = Double.NaN;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                if (startSignalPhyValue <= endSignalPhyValue) {
                    for (int val = startSignalPhyValue; val <= endSignalPhyValue; val += step) {
                        while (isPause()) {
                            log.info("CAN信号步进已暂停");
                            try {
                                synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                                    ActionSequenceLock.getInstance().getPauseLock().wait();
                                }
                            } catch (InterruptedException e) {
                                break;
                            }
                        }
                        pass = setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, val, 1);
                        signalValue = val;
                        try {
                            Thread.sleep(milliseconds);
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                } else {
                    for (int val = startSignalPhyValue; val >= endSignalPhyValue; val -= step) {
                        while (isPause()) {
                            log.info("CAN信号步进已暂停");
                            try {
                                synchronized (ActionSequenceLock.getInstance().getPauseLock()) {
                                    ActionSequenceLock.getInstance().getPauseLock().wait();
                                }
                            } catch (InterruptedException e) {
                                break;
                            }
                        }
                        pass = setCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName, val, 1);
                        signalValue = val;
                        try {
                            Thread.sleep(milliseconds);
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                }
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setStepCanSignal", pass, signalValue);
        return actualExpectedResult;
    }

    //设置Can信号步进值（物理值），无ECU
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL_WITHOUT_ECU"})
    default ActualExpectedResult setStepCanSignal(Integer deviceChannel, String messageName, String signalName, Integer lowerSignalPhyValue, Integer upperSignalPhyValue, Integer step, String stepInterval) {
        return setStepCanSignal(deviceChannel, null, messageName, signalName, lowerSignalPhyValue, upperSignalPhyValue, step, stepInterval);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).STEP_CAN_SIGNAL_WITHOUT_ECU_AND_TIME"})
    default ActualExpectedResult setStepCanSignal(Integer deviceChannel, String messageName, String signalName, Integer lowerSignalPhyValue, Integer upperSignalPhyValue, Integer step) {
        //获取dbc报文周期
        int messageCycle = getCanMessageCycle(deviceChannel, messageName);
        return setStepCanSignal(deviceChannel, messageName, signalName, lowerSignalPhyValue, upperSignalPhyValue, step, messageCycle + "ms");
    }


    /**
     * 设置Can报文checksum & rolling状态
     *
     * @param deviceChannel        Can通道
     * @param ecuNodeName          ECU节点名称
     * @param messageNameOrID      CAN报文名或ID
     * @param checksumStatus       Checksum状态（ 0/1，1表示正确，0表示错误）
     * @param rollingCounterStatus rollingCounter状态 （0/1，1表示正确，0表示错误）
     * @return 是否设置Can报文checksum & rolling成功
     */
    boolean setCanMessageCSRolling(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_MESSAGE_CSROLLING"})
    default ActualExpectedResult setCanMessageCSRollingInfo(Integer deviceChannel, String ecuNodeName, String messageNameOrID, int checksumStatus, int rollingCounterStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanMessageCSRolling(deviceChannel, ecuNodeName, messageNameOrID, checksumStatus, rollingCounterStatus);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanMessageCSRolling", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_MESSAGE_CSROLLING_WITHOUT_ECU"})
    default ActualExpectedResult setCanMessageCSRollingInfo(Integer deviceChannel, String messageNameOrID, int checksumStatus, int rollingCounterStatus) {
        return setCanMessageCSRollingInfo(deviceChannel, null, messageNameOrID, checksumStatus, rollingCounterStatus);
    }


    /**
     * 写入Can PTS
     *
     * @param deviceChannel   Can通道
     * @param ecuNodeName     ECU节点名称
     * @param byteInstruction 8个字节的指令
     * @return 是否设置Can PTS成功
     */
    boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) throws BusError;

    boolean setCanPTS(Integer deviceChannel, String ecuNodeName, String sendMessageId, String byteInstruction, String recvMessageId, boolean isChecked) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).CHANGE_CAN_PTS"})
    default ActualExpectedResult setCanPTSInfo(Integer deviceChannel, String ecuNodeName, String messageId, String byteInstruction, String checkedContext) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanPTS(deviceChannel, ecuNodeName, messageId, optimizeByteString(byteInstruction), checkedContext);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanPTS", pass, "");
        log.info("设置Can PTS响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).CHANGE_CAN_PTS_WITH_ECU"})
    default ActualExpectedResult setCanPTSInfo(Integer deviceChannel, String ecuNodeName, String byteInstruction, String checkedContext) {
        return setCanPTSInfo(deviceChannel, ecuNodeName, null, byteInstruction, checkedContext);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).CHANGE_CAN_PTS_WITHOUT_ECU"})
    default ActualExpectedResult setCanPTSInfo(Integer deviceChannel, String byteInstruction, String checkedContext) {
        return setCanPTSInfo(deviceChannel, null, null, byteInstruction, checkedContext);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).CHANGE_CAN_PTS_BY_MESSAGE_ID"})
    default ActualExpectedResult setCanPTSInfoByMessageId(Integer deviceChannel, String messageId, String byteInstruction, String checkedContext) {
        return setCanPTSInfo(deviceChannel, null, messageId, byteInstruction, checkedContext);
    }

    /**
     * 设置Can单个报文状态
     *
     * @param deviceChannel   Can通道
     * @param ecuNodeName     ECU节点名称
     * @param messageNameOrId CAN报文名称
     * @param messageStatus   CAN报文状态
     * @return 是否设置成功
     */
    boolean setCanSingleMsgStatus(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_SINGLE_CHANNEL_MESSAGE_STATUS"})
    default ActualExpectedResult setCanSingleMsgStatusInfo(Integer deviceChannel, String ecuNodeName, String messageNameOrId, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanSingleMsgStatus(deviceChannel, ecuNodeName, messageNameOrId, messageStatus);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanSingleMsgStatus", pass, messageStatus);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_SINGLE_CHANNEL_MESSAGE_STATUS_WITHOUT_ECU"})
    default ActualExpectedResult setCanSingleMsgStatusInfo(Integer deviceChannel, String messageNameOrId, int messageStatus) {
        return setCanSingleMsgStatusInfo(deviceChannel, null, messageNameOrId, messageStatus);
    }

    /**
     * 设置Can ECU报文状态
     *
     * @param deviceChannel Can通道
     * @param ecuNodeName   ECU节点名称
     * @param messageStatus CAN报文状态
     * @return 是否设置成功
     */
    boolean setCanEcuAllMsgStatus(Integer deviceChannel, String ecuNodeName, int messageStatus) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_ECU_MESSAGE_STATUS"})
    default ActualExpectedResult setCanEcuAllMsgStatusInfo(Integer deviceChannel, String ecuNodeName, int messageStatus) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanEcuAllMsgStatus(deviceChannel, ecuNodeName, messageStatus);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanEcuAllMsgStatus", pass, messageStatus);
        return actualExpectedResult;
    }

    boolean setCanMessageDLC(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) throws BusError;

    /**
     * 设置报文DLC
     *
     * @param deviceChannel   Can通道
     * @param ecuNodeName     ECU节点名称
     * @param messageNameOrId 报文名
     * @param dlc             DLC长度
     * @return
     */
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_DLC"})
    default ActualExpectedResult setCanMessageDLCInfo(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double dlc) {
        log.info("setCanMessageDLCInfo ecuNodeName:{}, messageNameOrId:{}, dlc:{}", ecuNodeName, messageNameOrId, dlc);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanMessageDLC(deviceChannel, ecuNodeName, messageNameOrId, dlc);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
            dlc = Double.NaN;
        }
        actualExpectedResult.put("setCanMessageDLC", pass, dlc);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_DLC_WITHOUT_ECU"})
    default ActualExpectedResult setCanMessageDLCInfo(Integer deviceChannel, String messageNameOrId, double dlc) {
        return setCanMessageDLCInfo(deviceChannel, null, messageNameOrId, dlc);
    }

    boolean setCanMessageCycleTime(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_CYCLE_TIME"})
    default ActualExpectedResult setCanMessageCycleTimeInfo(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double cycleTime) {
        log.info("setCanMessageCycleTimeInfo ecuNodeName:{}, messageNameOrId:{}, cycleTime:{}", ecuNodeName, messageNameOrId, cycleTime);
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanMessageCycleTime(deviceChannel, ecuNodeName, messageNameOrId, cycleTime);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
            cycleTime = Double.NaN;
        }
        actualExpectedResult.put("setCanMessageCycleTime", pass, cycleTime);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).CHANGE_CAN_CYCLE_TIME_WITHOUT_ECU"})
    default ActualExpectedResult setCanMessageCycleTimeInfo(Integer deviceChannel, String messageNameOrId, double cycleTime) {
        return setCanMessageCycleTimeInfo(deviceChannel, null, messageNameOrId, cycleTime);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).RANDOM_CHANGE_CAN_CYCLE_TIME"})
    default ActualExpectedResult setCanMessageRandomCycleTimeInfo(Integer deviceChannel, String ecuNodeName, String messageNameOrId, double lowerCycleTime, double upperCycleTime) {
        return setCanMessageCycleTimeInfo(deviceChannel, ecuNodeName, messageNameOrId, RandomUtil.randomInt((int) lowerCycleTime, (int) upperCycleTime));
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Property).RANDOM_CHANGE_CAN_CYCLE_TIME_WITHOUT_ECU"})
    default ActualExpectedResult setCanMessageRandomCycleTimeInfo(Integer deviceChannel, String messageNameOrId, double lowerCycleTime, double upperCycleTime) {
        return setCanMessageCycleTimeInfo(deviceChannel, null, messageNameOrId, RandomUtil.randomDouble(lowerCycleTime, upperCycleTime));
    }

    /**
     * 获取Can信号值（物理值）
     *
     * @param deviceChannel Can通道
     * @param ecuNodeName   ECU节点名称
     * @param signalName    信号名称
     * @return Can信号值（物理值）
     */
    double fetchCanSignalPhyValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError;

    double fetchCanSignalRawValue(Integer deviceChannel, String ecuNodeName, String messageName, String signalName) throws SimulatedDeviceNotification, BusError;

    Map<String, DecodedSignal> fetchAllCanSignalValue(Integer deviceChannel, String messageName) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).COMPARE_CAN_SIGNAL"})
    default ActualExpectedResult compareCanSignal(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String signalValueString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //判断signalPhyValueString是否是16进制0x开头的字符串,是就转成double类型
        boolean isHex = signalValueString.matches("^0x[0-9a-fA-F]+$");
        double signalValue = isHex ? Long.decode(signalValueString) : removeParenthesesToDouble(signalValueString);
        double canSignal = 0;
        boolean pass = false;
        boolean simualate = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                canSignal = isHex ? fetchCanSignalRawValue(deviceChannel, ecuNodeName, messageName, signalName) : fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
                pass = canSignal == signalValue;
                if (!isFailReTry()) {
                    break;
                }
                if (pass) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }

            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
            canSignal = Double.NaN;
        } catch (SimulatedDeviceNotification e) {
            simualate = true;
            pass = true;
            canSignal = Double.NaN;
        }
        if (simualate) {
            log.info("Can报文信号期望值:{},实际获取信号值:{}，模拟设备，不做判断", signalValueString, canSignal);
        } else {
            log.info("Can报文信号期望值:{},实际获取信号值:{},检测结果:{}", signalValueString, canSignal, pass ? "通过" : "失败");
        }
        //如果是isHex是true，把canSignal转成16进制字符串
        actualExpectedResult.put("compareCanSignal", pass, isHex ? doubleToHexString(canSignal) : canSignal);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).COMPARE_CAN_SIGNAL_WITHOUT_ECU"})
    default ActualExpectedResult compareCanSignal(Integer deviceChannel, String messageName, String signalName, String signalPhyValueString) {
        return compareCanSignal(deviceChannel, null, messageName, signalName, signalPhyValueString);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).COMPARE_CAN_SIGNAL_RANGE"})
    default ActualExpectedResult compareCanSignalRange(Integer deviceChannel, String ecuNodeName, String messageName, String signalName, String lowerPhySignalString, String upperPhySignalString) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        double lowerSignal = removeParenthesesToDouble(lowerPhySignalString);
        double upperSignal = removeParenthesesToDouble(upperPhySignalString);
        double canSignal = Double.NaN;
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                canSignal = fetchCanSignalPhyValue(deviceChannel, ecuNodeName, messageName, signalName);
                pass = canSignal >= lowerSignal && canSignal <= upperSignal;
                if (!isFailReTry()) {
                    break;
                }
                if (pass) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
            canSignal = Double.NaN;
        } catch (SimulatedDeviceNotification e) {
            pass = true;
            canSignal = Double.NaN;
        }
        log.info("Can报文信号值期望范围:{}~{},实际获取信号值:{},检测结果:{}", lowerSignal, upperSignal, canSignal, pass ? "通过" : "失败");
        actualExpectedResult.putDefault(pass, canSignal);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Signal).COMPARE_CAN_SIGNAL_RANGE_WITHOUT_ECU"})
    default ActualExpectedResult compareCanSignalRangeWithoutECU(Integer deviceChannel, String messageName, String signalName, String lowerPhySignalString, String upperPhySignalString) {
        return compareCanSignalRange(deviceChannel, null, messageName, signalName, lowerPhySignalString, upperPhySignalString);
    }

    String fetchCanPTS(Integer deviceChannel, String messageId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_WITHOUT_MESSAGE_VALUE"})
    default ActualExpectedResult compareCanPTSNotMessageValue(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String rxByteInstruction = "";
        for (int i = 0; i < RETRY_TIMES; i++) {
            rxByteInstruction = fetchCanPTS(deviceChannel, messageId);
            if ("NA".equals(byteInstruction)) {
                pass = byteInstruction.equals(rxByteInstruction);
            } else {
                pass = rxByteInstruction.isEmpty() || !compareStrings(byteInstruction, rxByteInstruction, 'X');
            }
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("Can PTS报文期望值:{},实际获取报文:{},检测结果:{}", byteInstruction, rxByteInstruction, pass ? "通过" : "失败");
        actualExpectedResult.put("compareCanPTS", pass, rxByteInstruction);
        log.info("获取Can PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_WITH_MESSAGE_ID"})
    default ActualExpectedResult compareCanPTS(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String rxByteInstruction = "";
        for (int i = 0; i < RETRY_TIMES; i++) {
            rxByteInstruction = fetchCanPTS(deviceChannel, messageId);
            if ("NA".equals(byteInstruction)) {
                pass = byteInstruction.equals(rxByteInstruction);
            } else {
                pass = !rxByteInstruction.isEmpty() && compareStrings(byteInstruction, rxByteInstruction, 'X');
            }
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("Can PTS报文期望值:{},实际获取报文:{},检测结果:{}", byteInstruction, rxByteInstruction, pass ? "通过" : "失败");
        actualExpectedResult.put("compareCanPTS", pass, rxByteInstruction);
        log.info("获取Can PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean fetchCanMsgID(Integer deviceChannel, String messageId, boolean exist) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).EXIST_CAN_MESSAGE_ID"})
    default ActualExpectedResult compareExistCanMessageId(Integer deviceChannel, String messageId) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = fetchCanMsgID(deviceChannel, messageId, true);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("期望CAN总线上出现指定报文ID:{}, 检测结果:{}", messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareExistCanMessageId", pass, pass ? messageId : "NA");
        log.info("CAN总线上出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).NOT_EXIST_CAN_MESSAGE_ID"})
    default ActualExpectedResult compareNotExistCanMessageId(Integer deviceChannel, String messageId) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = fetchCanMsgID(deviceChannel, messageId, false);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("期望CAN总线上不出现指定报文ID:{}, 检测结果:{}", messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistCanMessageId", pass, "NA");
        log.info("CAN总线上不出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }


    boolean lastCheckCanMsgID(Integer deviceChannel, String messageId, boolean exist, Integer milliSecond) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).LAST_CHECK_EXIST_CAN_MESSAGE_ID"})
    default ActualExpectedResult lastCheckExistCanMessageIdInfo(Integer deviceChannel, String messageId, Integer milliSecond) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = lastCheckCanMsgID(deviceChannel, messageId, true, milliSecond);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("持续{}ms检测CAN总线上出现指定报文ID:{}, 检测结果:{}", milliSecond, messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistCanMessageId", pass, "NA");
        log.info("持续检测CAN总线上出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).LAST_CHECK_NOT_EXIST_CAN_MESSAGE_ID"})
    default ActualExpectedResult lastCheckNotExistCanMessageIdInfo(Integer deviceChannel, String messageId, Integer milliSecond) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            pass = lastCheckCanMsgID(deviceChannel, messageId, false, milliSecond);
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            } else {
                break;
            }
        }
        log.info("持续{}ms检测CAN总线上不出现指定报文ID:{}, 检测结果:{}", milliSecond, messageId, pass ? "通过" : "失败");
        actualExpectedResult.put("compareNotExistCanMessageId", pass, "NA");
        log.info("持续检测CAN总线上不出现指定报文结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    default ActualExpectedResult compareCanScript(Integer deviceChannel, String messageId, String byteInstruction) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String rxByteInstruction = fetchCanPTS(deviceChannel, messageId);
        boolean pass;
        if ("NA".equals(byteInstruction)) {
            pass = byteInstruction.equals(rxByteInstruction);
        } else {
            pass = !rxByteInstruction.isEmpty() && byteInstruction.equals(rxByteInstruction);
        }
        log.info("Can PTS报文期望值:{},实际获取报文:{},检测结果:{}", byteInstruction, rxByteInstruction, pass ? "通过" : "失败");
        actualExpectedResult.put("compareCanPTS", pass, rxByteInstruction);
        log.info("获取Can PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS"})
    default ActualExpectedResult compareCanPTS(Integer deviceChannel, String byteInstruction) throws BusError {
        return compareCanPTS(deviceChannel, null, byteInstruction);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_NA"})
    default ActualExpectedResult withoutCanPTS(Integer deviceChannel) throws BusError {
        return compareCanPTS(deviceChannel, null, "NA");
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_BYTE_WITH_MESSAGE_ID"})
    default ActualExpectedResult compareCanPTSByte(Integer deviceChannel, String messageId, String byteInstruction, int byteLocation, int lower, int upper) throws BusError {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String rxByteInstruction = "";
        boolean pass = false;
        for (int i = 0; i < RETRY_TIMES; i++) {
            rxByteInstruction = fetchCanPTS(deviceChannel, messageId).replaceAll("\\s", "");
            boolean matchWithoutXX = compareStrings(byteInstruction, rxByteInstruction, 'X');
            if (matchWithoutXX) {
                //rxByteInstruction的第几个byte的上下限有没有在范围内
//            int byteLocation = findNumberByString(byteLocationString);
                if (byteLocation != -1 && byteLocation < 8) {
                    String byteValue = splitString(rxByteInstruction, byteLocation, 2);
//                int byteValueInt = Integer.parseInt(byteValue == null ? "-1" : byteValue);
                    int byteValueInt = Integer.parseInt(byteValue, 16);
                    pass = byteValueInt >= lower && byteValueInt <= upper;
                    log.info("Can PTS实获取期望报文:{}, 获取实际报文:{}, 字节位:{}, 字节值:{}, 期望范围:{}~{},检测结果:{}", byteInstruction, rxByteInstruction, byteLocation, byteValueInt, lower, upper, pass ? "通过" : "失败");
                } else {
                    log.info("  请检查byte位置的脚本格式{}, 位置：0~7, 正确格式：byte0~byte7", byteLocation);
                }
            }
            if (!isFailReTry()) {
                break;
            }
            if (!pass) {
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        actualExpectedResult.put("compareCanPTSByte", pass, rxByteInstruction);
        log.info("获取compareCanPTSByte, 结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_BYTE"})
    default ActualExpectedResult compareCanPTSByte(Integer deviceChannel, String byteInstruction, int byteLocation, int lower, int upper) throws BusError {
        return compareCanPTSByte(deviceChannel, null, byteInstruction, byteLocation, lower, upper);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_HEX_BYTE"})
    default ActualExpectedResult compareCanPTSHexByte(Integer deviceChannel, String byteInstruction, int byteLocation, String StandardValue, String FloatingValue) throws BusError {
        int lower = additionHex(StandardValue, FloatingValue);
        int upper = subtractionHex(StandardValue, FloatingValue);
        return compareCanPTSByte(deviceChannel, null, byteInstruction, byteLocation, lower, upper);
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_HEX_BYTE_WITH_MESSAGE_ID"})
    default ActualExpectedResult compareCanPTSHexByte(Integer deviceChannel, String messageId, String byteInstruction, int byteLocation, String StandardValue, String FloatingValue) throws BusError {
        int lower = additionHex(StandardValue, FloatingValue);
        int upper = subtractionHex(StandardValue, FloatingValue);
        return compareCanPTSByte(deviceChannel, messageId, byteInstruction, byteLocation, lower, upper);
    }

//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_TX_RX"})
//    default ActualExpectedResult compareCanPTSTXRX(Integer deviceChannel, String txByteInstruction, String rxByteInstruction) throws BusError {
//        long startMills = System.currentTimeMillis();
//        setCanPTS(deviceChannel, null, null, txByteInstruction);
//
//        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
//        String byteInstruction = fetchCanPTS(deviceChannel, null);
//        boolean pass = !byteInstruction.isEmpty() && compareStrings(rxByteInstruction, byteInstruction, 'X');
//        log.info("Can PTS报文期望值:{},实际获取报文:{},检测结果:{}", rxByteInstruction, byteInstruction, pass ? "通过" : "失败");
//        actualExpectedResult.put("compareCanPTS", pass, rxByteInstruction);
//        log.info("获取Can PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
//        return actualExpectedResult;
//    }
//
//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.PTS).COMPARE_CAN_PTS_TX_RX_WITH_MESSAGE_ID"})
//    default ActualExpectedResult compareCanPTSTXRX(Integer deviceChannel, String txMessageId, String txByteInstruction, String rxMessageId, String rxByteInstruction) throws BusError {
//        long startMills = System.currentTimeMillis();
//        setCanPTS(deviceChannel, null, txMessageId, txByteInstruction);
//        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
//        String byteInstruction = fetchCanPTS(deviceChannel, rxMessageId);
//        boolean pass = !byteInstruction.isEmpty() && compareStrings(rxByteInstruction, byteInstruction, 'X');
//        log.info("Can PTS报文期望值:{},实际获取报文:{},检测结果:{}", rxByteInstruction, byteInstruction, pass ? "通过" : "失败");
//        actualExpectedResult.put("compareCanPTS", pass, rxByteInstruction);
//        log.info("获取Can PTS结果反馈响应事件耗时:{}毫秒", System.currentTimeMillis() - startMills);
//        return actualExpectedResult;
//    }

    /**
     * 触发升级
     *
     * @param fileType 文件类型
     * @return 是否升级成功
     */
    String notificationUpgrade(int fileType) throws BusError;

    boolean compareVersion(String ptsSwVersion);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).NOTIFICATION_UPGRADE"})
    default ActualExpectedResult notificationUpdateInfo(int fileType) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String ptsSwVersion = null;
        try {
            ptsSwVersion = notificationUpgrade(fileType);
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        boolean pass = compareVersion(ptsSwVersion);
        log.info("升级结果:{}, 响应事件耗时:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("notificationUpdateInfo", pass, fileType);
        return actualExpectedResult;
    }

    double fetchXCPRX(String ecuNodeName, String xcpName) throws BusError;

    boolean setXCP(String ecuNodeName, String xcpName, double xcpValue) throws BusError;
    //以前长城项目的XCP序列不用了，与宝腾项目新的XCP序列有冲突了，先mark起来
//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHANGE_XCP"})
//    default ActualExpectedResult setXCPInfo(String ecuNode, String xcpName, String xcpValueString) {
//        long startMills = System.currentTimeMillis();
//        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
//        double xcpValue = removeParenthesesToDouble(xcpValueString);
//        boolean pass = false;
//        try {
//            for (int i = 0; i < RETRY_TIMES; i++) {
//                pass = setXCP(ecuNode, xcpName, xcpValue);
//                if (!isFailReTry()) {
//                    break;
//                }
//                if (!pass) {
//                    try {
//                        Thread.sleep(RETRY_INTERVAL);
//                    } catch (InterruptedException e) {
//                        log.error(e.getMessage(), e);
//                    }
//                } else {
//                    break;
//                }
//            }
//        } catch (BusError e) {
//            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
//        }
//        actualExpectedResult.put("setXCPInfo", pass, xcpValueString);
//        log.info("setXCPInfo响应事件:{}, 耗时:{}毫秒", pass ? "成功" : "失败", System.currentTimeMillis() - startMills);
//        return actualExpectedResult;
//    }
//
//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).COMPARE_XCP"})
//    default ActualExpectedResult compareXCP(String ecuNode, String xcpName, String expectXcpValueString) {
//        long startMills = System.currentTimeMillis();
//        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
//        double expectXcpValue = removeParenthesesToDouble(expectXcpValueString);
//        double xcp = 0;
//        boolean pass = false;
//        try {
//            for (int i = 0; i < RETRY_TIMES; i++) {
//                xcp = fetchXCPRX(ecuNode, xcpName);
//                pass = xcp == expectXcpValue;
//                if (!isFailReTry()) {
//                    break;
//                }
//                if (!pass) {
//                    try {
//                        Thread.sleep(RETRY_INTERVAL);
//                    } catch (InterruptedException e) {
//                        log.error(e.getMessage(), e);
//                    }
//                } else {
//                    break;
//                }
//            }
//        } catch (BusError e) {
//            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
//            xcp = Double.NaN;
//        }
//        log.info("XCP信号期望值:{},实际获取信号值:{},检测结果:{},共耗时:{}毫秒", expectXcpValue, xcp, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
//        actualExpectedResult.put("compareXCPRX", pass, xcp);
//        return actualExpectedResult;
//    }
//
//    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).COMPARE_XCP_RANGE"})
//    default ActualExpectedResult compareXCPRange(String ecuNode, String xcpName, String lowerXcpValueString, String upperXcpValueString) {
//        long startMills = System.currentTimeMillis();
//        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
//        double lowerXcpValue = removeParenthesesToDouble(lowerXcpValueString);
//        double upperXcpValue = removeParenthesesToDouble(upperXcpValueString);
//        double xcp = 0;
//        boolean pass = false;
//        try {
//            for (int i = 0; i < RETRY_TIMES; i++) {
//                xcp = fetchXCPRX(ecuNode, xcpName);
//                pass = xcp >= lowerXcpValue && xcp <= upperXcpValue;
//                if (!isFailReTry()) {
//                    break;
//                }
//                if (!pass) {
//                    try {
//                        Thread.sleep(RETRY_INTERVAL);
//                    } catch (InterruptedException e) {
//                        log.error(e.getMessage(), e);
//                    }
//                } else {
//                    break;
//                }
//            }
//        } catch (BusError e) {
//            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
//            xcp = Double.NaN;
//        }
//        log.info("XCP信号期望范围:{}~{},实际获取信号值:{},检测结果:{},共耗时:{}毫秒", lowerXcpValue, upperXcpValue, xcp, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
//        actualExpectedResult.put("compareXCPRange", pass, xcp);
//        return actualExpectedResult;
//    }


    boolean setCanLogName(String canLogName) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_LOG_NAME"})
    default ActualExpectedResult setCanLogNameInfo(String canLogName) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanLogName(canLogName);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        actualExpectedResult.put("setCanLogNameInfo", pass, canLogName);
        log.info("执行CAN中setCanLogName canLogName:{},检测结果:{},共耗时:{}毫秒", canLogName, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        return actualExpectedResult;
    }

    boolean setCanLog(Integer deviceChannel, int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_CAN_LOG"})
    default ActualExpectedResult setCanLogInfo(Integer deviceChannel, int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setCanLog(deviceChannel, commandId);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行CAN Log:{},检测结果:{},共耗时:{}毫秒", commandId == 0 ? "开始录制" : "结束录制", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setCanLogInfo", pass, commandId);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).STOP_CAN_CHANNEL"})
    default ActualExpectedResult stopCanChannel(Integer deviceChannel) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = closeChannel(deviceChannel);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        } catch (DeviceCloseException e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行closeChannel:{},结果:{},共耗时:{}毫秒", deviceChannel, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("stopCanChannel", pass);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).START_CAN_CHANNEL"})
    default ActualExpectedResult startCanChannel(Integer deviceChannel) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = openChannel(deviceChannel);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        } catch (DeviceOpenException e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行closeChannel:{},结果:{},共耗时:{}毫秒", deviceChannel, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("startCanChannel", pass);
        return actualExpectedResult;
    }

    boolean setIGSendCommand(Integer deviceChannel, String igTabName, int command) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_IG_SEND"})
    default ActualExpectedResult setIGSendInfo(Integer deviceChannel, String igTabName, int command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setIGSendCommand(deviceChannel, igTabName, command);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setIGSendInfo:通道{},IG模块：{}-->{},检测结果:{},共耗时:{}毫秒", deviceChannel, igTabName, command == 0 ? "发送" : "停止", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setIGSendInfo", pass, command);
        return actualExpectedResult;
    }

    boolean setIGSendAllCommand(Integer deviceChannel, int command) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_IG_SEND_ALL"})
    default ActualExpectedResult setIGSendAllInfo(Integer deviceChannel, int command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setIGSendAllCommand(deviceChannel, command);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setIGSendInfo:通道{}全部IG模块-->{},检测结果:{},共耗时:{}毫秒", deviceChannel, command == 0 ? "发送" : "停止", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setIGSendInfo", pass, command);
        return actualExpectedResult;
    }

    boolean setXcpFunSwitch(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_XCP_FUN_SWITCH"})
    default ActualExpectedResult setXcpSwitchInfo(Integer deviceChannel, int switchCommand) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setXcpFunSwitch(switchCommand);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setXcpSwitch:{},检测结果:{},共耗时:{}毫秒", switchCommand, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setXcpSwitchInfo", pass, switchCommand);
        return actualExpectedResult;
    }

    boolean setXcpVar(String varName, int xcpValue) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_XCP_VAR"})
    default ActualExpectedResult setXcpVarInfo(Integer deviceChannel, String varName, int varValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //处理varName中有{变成[, }变成]
        varName = varName.replace("{", "[").replace("}", "]");
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                boolean switchOpenPass = setXcpFunSwitch(1);
                boolean setVarPass = setXcpVar(varName, varValue);
                boolean switchClosePass = setXcpFunSwitch(0);
                pass = switchOpenPass && setVarPass && switchClosePass;
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setXcpVar 变量名：{}， 变量值:{},检测结果:{},共耗时:{}毫秒", varName, varValue, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setXcpVarInfo", pass, varValue);
        return actualExpectedResult;
    }

    boolean setXcpSwitchAndVar(int switchCommand, String varName, int varValue) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_XCP_FUN_SWITCH_AND_VAR"})
    default ActualExpectedResult setXcpSwitchAndVarInfo(Integer deviceChannel, int switchCommand, String varName, int varValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //处理varName中有{变成[, }变成]
        varName = varName.replace("{", "[").replace("}", "]");
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setXcpSwitchAndVar(switchCommand, varName, varValue);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setXcpSwitchAndVarInfo XCP功能状态:{}, 变量名：{}， 变量值:{},检测结果:{},共耗时:{}毫秒", switchCommand, varName, varValue, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setXcpVarInfo", pass, switchCommand);
        return actualExpectedResult;
    }


    boolean setKeyPosition(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_KEY_POSITION"})
    default ActualExpectedResult setKeyPositionInfo(Integer deviceChannel, int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setKeyPosition(commandId);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setKeyPosition:{},检测结果:{},共耗时:{}毫秒", commandId, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setKeyPositionInfo", pass, commandId);
        return actualExpectedResult;
    }


    boolean setKeyButton(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_KEY_BUTTON"})
    default ActualExpectedResult setKeyButtonInfo(Integer deviceChannel, int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setKeyButton(commandId);
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setKeyButton:{},检测结果:{},共耗时:{}毫秒", commandId, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setKeyButtonInfo", pass, commandId);
        return actualExpectedResult;
    }

    boolean setRDefogSts(int commandId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_RDEFOGSTS"})
    default ActualExpectedResult setRDefogStsInfo(Integer deviceChannel, int commandId) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setRDefogSts(commandId);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setRDefogSts:{},检测结果:{},共耗时:{}毫秒", commandId, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setRDefogStsInfo", pass, commandId);
        return actualExpectedResult;
    }


    boolean setMirrorFoldSTS(String command) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_MIRRORFOLDSTS"})
    default ActualExpectedResult setMirrorFoldSTSInfo(Integer deviceChannel, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setMirrorFoldSTS(command);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setMirrorFoldSTS:{},检测结果:{},共耗时:{}毫秒", command, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setMirrorFoldSTSInfo", pass, command);
        return actualExpectedResult;
    }

    boolean setLampSwitch(String command) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SET_LAMP_SWITCH"})
    default ActualExpectedResult setLampSwitchInfo(Integer deviceChannel, String command) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = setLampSwitch(command);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行setLampSwitch:{},检测结果:{},共耗时:{}毫秒", command, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("setLampSwitchInfo", pass, command);
        return actualExpectedResult;
    }

    boolean checkTurnLamp(String turnLampType, int workTime, int checkPeriod) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_TURN_LAMP"})
    default ActualExpectedResult checkTurnLampInfo(Integer deviceChannel, String turnLampType, int workTime, int checkPeriod) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = checkTurnLamp(turnLampType, workTime, checkPeriod);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkTurnLampInfo:{},检测结果:{},共耗时:{}毫秒", "", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkTurnLampInfo", pass, "");
        return actualExpectedResult;
    }


    boolean checkFourDoor(String lockStatusCommand) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_FOUR_DOOR"})
    default ActualExpectedResult checkFourDoorInfo(Integer deviceChannel, String lockStatusCommand) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = checkFourDoor(lockStatusCommand);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkFourDoorInfo:{},检测结果:{},共耗时:{}毫秒", lockStatusCommand, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkFourDoorInfo", pass, lockStatusCommand);
        return actualExpectedResult;
    }


    boolean sendEventMsg(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).SEND_EVENT_MSG"})
    default ActualExpectedResult sendEventMsgInfo(Integer deviceChannel, String MsgID, int msgTime, int msgCounter, String msgData) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = sendEventMsg(deviceChannel, MsgID, msgTime, msgCounter, msgData);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行sendEventMsgInfo:{},检测结果:{},共耗时:{}毫秒", MsgID, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("sendEventMsgInfo", pass, "");
        return actualExpectedResult;
    }

    default boolean checkVoltage(int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) throws BusError {
        return false;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_VOLTAGE"})
    default ActualExpectedResult checkVoltageInfo(Integer deviceChannel, int pinNumber, int pinAliveTime, int pinNoAliveTime, int workCycleNumber) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                pass = checkVoltage(pinNumber, pinAliveTime, pinNoAliveTime, workCycleNumber);
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkVoltageInfo检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkVoltageInfo", pass, "");
        return actualExpectedResult;
    }

    double fetchMsgCycleTime(Integer deviceChannel, String messageId) throws BusError;

    //cycleTimeExpectValue/deviation时间单位为ms
    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_MSG_CYCLE_TIME"})
    default ActualExpectedResult checkMsgCycleTimeInfo(Integer deviceChannel, String messageId, double cycleTimeExpectValue, double deviation) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        double cycleTime = 0;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                cycleTime = fetchMsgCycleTime(deviceChannel, messageId);
                // 实际周期时间 cycleTime 与期望值 cycleTimeExpectValue 的绝对差是否小于等于允许的偏差 deviation，若满足条件则表示通过检测（pass 为 true）。
                pass = Math.abs(cycleTime - cycleTimeExpectValue) <= deviation;
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkMsgCycleTimeInfo检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkMsgCycleTimeInfo", pass, cycleTime);
        return actualExpectedResult;
    }

    int fetchMsgDLC(Integer deviceChannel, String messageId) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_MSG_DLC"})
    default ActualExpectedResult checkMsgDLCInfo(Integer deviceChannel, String messageId, int dlc) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        int msgDLC = 0;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                msgDLC = fetchMsgDLC(deviceChannel, messageId);
                pass = msgDLC == dlc;
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkMsgDLCInfo检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkMsgDLCInfo", pass, msgDLC);
        return actualExpectedResult;
    }


    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_MSG_CYCLE_TIME_AND_DLC"})
    default ActualExpectedResult checkMsgCycleTimeAndDLCInfo(Integer deviceChannel, String messageId, int cycleTimeExpectValue, int deviation, int dlc) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        double cycleTime = 0;
        int msgDLC = 0;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                cycleTime = fetchMsgCycleTime(deviceChannel, messageId);
                log.info("cycleTime:{}", cycleTime);
                // 实际周期时间 cycleTime 与期望值 cycleTimeExpectValue 的绝对差是否小于等于允许的偏差 deviation，若满足条件则表示通过检测（pass 为 true）。
                msgDLC = fetchMsgDLC(deviceChannel, messageId);
                log.info("msgDLC:{}", msgDLC);
                pass = Math.abs(cycleTime - cycleTimeExpectValue) <= deviation && msgDLC == dlc;
                if (pass) break;
                if (!isFailReTry()) {
                    break;
                }
                try {
                    Thread.sleep(RETRY_INTERVAL);
                } catch (InterruptedException e) {
                    log.error(e.getMessage(), e);
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("执行checkMsgCycleTimeAndDLCInfo检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkMsgCycleTimeAndDLCInfo", pass, String.format("%s-%d", cycleTime, msgDLC));
        return actualExpectedResult;


    }

    int fetchXcpVar(String varName) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_XCP_VAR_WITH_TIME"})
    default ActualExpectedResult checkXcpVarInfo(Integer deviceChannel, int switchCommand_1, String varName, int varValue, String timeWithUnit, int switchCommand_2) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //处理varName中有{变成[, }变成]
        varName = varName.replace("{", "[").replace("}", "]");
        int xcpValue = 0;
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                boolean switchCommand_1Pass = setXcpFunSwitch(switchCommand_1);
                xcpValue = fetchXcpVar(varName);
                Float seconds = BaseRegexRule.getSecondsOfDefaultMills(timeWithUnit);
                if (seconds != null) {
                    waitTime(seconds.longValue());
                }
                boolean switchCommand_2Pass = setXcpFunSwitch(switchCommand_2);
                pass = switchCommand_1Pass && xcpValue == varValue && switchCommand_2Pass;
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("checkXcpVarInfo 期望值:{},实际获取信号值:{},检测结果:{},共耗时:{}毫秒", varValue, xcpValue, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkXcpVarInfo", pass, xcpValue);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_XCP_VAR"})
    default ActualExpectedResult checkXcpVarInfo(Integer deviceChannel, String varName, int varValue) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        //处理varName中有{变成[, }变成]
        varName = varName.replace("{", "[").replace("}", "]");
        int xcpValue = 0;
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                boolean switchOpenPass = setXcpFunSwitch(1);
                xcpValue = fetchXcpVar(varName);
                boolean switchClosePass = setXcpFunSwitch(0);
                pass = switchOpenPass && xcpValue == varValue && switchClosePass;
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("checkXcpVarInfo 期望值:{},实际获取信号值:{},检测结果:{},共耗时:{}毫秒", varValue, xcpValue, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkXcpVarInfo", pass, xcpValue);
        return actualExpectedResult;
    }

    boolean checkFindKeyOrNoKey(boolean findKey, int findKeyTime) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_XCP_FIND_NO_KEY"})
    default ActualExpectedResult checkXcpFindNoKeyInfo(Integer deviceChannel, int findKeyTime) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                boolean switchOpenPass = setXcpFunSwitch(1);
                boolean checkPass = checkFindKeyOrNoKey(false, findKeyTime);
                boolean switchClosePass = setXcpFunSwitch(0);
                pass = switchOpenPass && checkPass && switchClosePass;
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("checkXcpFindNoKeyInfo 检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkXcpFindNoKeyInfo", pass, "");
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).CHECK_XCP_FIND_THE_KEY"})
    default ActualExpectedResult checkXcpFindTheKeyInfo(Integer deviceChannel, int findKeyTime) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        try {
            for (int i = 0; i < RETRY_TIMES; i++) {
                boolean switchOpenPass = setXcpFunSwitch(1);
                boolean checkPass = checkFindKeyOrNoKey(true, findKeyTime);
                boolean switchClosePass = setXcpFunSwitch(0);
                pass = switchOpenPass && checkPass && switchClosePass;
                if (!isFailReTry()) {
                    break;
                }
                if (!pass) {
                    try {
                        Thread.sleep(RETRY_INTERVAL);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage(), e);
                    }
                } else {
                    break;
                }
            }
        } catch (BusError e) {
            ActionSequencesLoggerUtil.warn(e.getMessage(), e);
        }
        log.info("checkXcpFindTheKeyInfo 检测结果:{},共耗时:{}毫秒", pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
        actualExpectedResult.put("checkXcpFindTheKeyInfo", pass, "");
        return actualExpectedResult;
    }

    /**
     * 检查CAN通道报文是否存在
     *
     * @param deviceChannel CAN通道
     * @param messageId     CAN报文ID
     * @param byteData      CAN报文数据通道报文状态
     * @return
     */
    String verifyCanMessage(Integer deviceChannel, String messageId, String byteData, Integer count) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_VERIFY_ID"})
    default ActualExpectedResult verifyMessage(Integer deviceChannel, String messageId) {
        String byteData = "XXXXXXXXXXXXXXXX";
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String data = "";
        try {
            data = verifyCanMessage(deviceChannel, messageId, byteData, 1);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        if (!data.isEmpty()) {
            pass = compareStrings(byteData, data, 'X');
        }
        actualExpectedResult.put("verifyCanMessage", pass, data);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_VERIFY_MESSAGE"})
    default ActualExpectedResult verifyMessage(Integer deviceChannel, String messageId, String byteData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String data = "";
        try {
            data = verifyCanMessage(deviceChannel, messageId, byteData, 1);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        if (!data.isEmpty()) {
            pass = compareStrings(byteData, data, 'X');
        }
        actualExpectedResult.put("verifyCanMessage", pass, data);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_VERIFY_MESSAGE_COUNT"})
    default ActualExpectedResult verifyMessage(Integer deviceChannel, String messageId, String byteData, Integer count) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = false;
        String data = "";
        try {
            data = verifyCanMessage(deviceChannel, messageId, byteData, count);
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        if (!data.isEmpty()) {
            pass = compareStrings(byteData, data, 'X');
        }
        actualExpectedResult.put("verifyCanMessage", pass, data);
        return actualExpectedResult;
    }

    /**
     * 检查是否唤醒设备
     *
     * @param deviceChannel CAN通道
     * @return
     */
    boolean wake(Integer deviceChannel, Integer time);

    /**
     * 检查设备是否休眠
     *
     * @param deviceChannel CAN通道
     * @return
     */
    boolean sleep(Integer deviceChannel, Integer time);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_CHECK_WAKE"})
    default ActualExpectedResult checkWake(Integer deviceChannel, Integer time) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = wake(deviceChannel, time);
        actualExpectedResult.put("wake", pass);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_CHECK_SLEEP"})
    default ActualExpectedResult checkSleep(Integer deviceChannel, Integer time) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = sleep(deviceChannel, time);
        actualExpectedResult.put("sleep", pass);
        return actualExpectedResult;
    }

    void sendService(Integer deviceChannel, String messageId, String byteData);

    boolean sendDatas(Integer deviceChannel, String messageId, String byteData);

    String checkReplyData(Integer deviceChannel, String messageId, String byteData);

    boolean responsiveServices(UdsModel udsModel);

    boolean responsiveService(Integer deviceChannel, String requestId, String responseId, String serviceData, String responseData);

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_SEND_SERVICE"})
    default ActualExpectedResult sendServe(Integer deviceChannel, String messageId, String byteData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        sendService(deviceChannel, messageId, byteData);
        actualExpectedResult.put("sendServe", true);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_UDS_DATA"})
    default ActualExpectedResult sendData(Integer deviceChannel, String byteData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = sendDatas(deviceChannel, "", byteData);
        actualExpectedResult.put("sendData", pass);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_SEND_DATA"})
    default ActualExpectedResult sendData(Integer deviceChannel, String messageId, String byteData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = sendDatas(deviceChannel, messageId, byteData);
        actualExpectedResult.put("sendData", pass);
        return actualExpectedResult;
    }


    String fetchCanUdsData(Integer deviceChannel, String expectResult) throws BusError;

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule).GET_CAN_UDS_FUN"})
    default ActualExpectedResult fetchCanUdsDataInfo(Integer deviceChannel, String expectResult) {
        long startMills = System.currentTimeMillis();
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        expectResult = expectResult.replaceAll("\\s", "");
        boolean pass = false;
        String resUdsData  = null;
        try {
            resUdsData = fetchCanUdsData(deviceChannel, expectResult);
            if (expectResult.equalsIgnoreCase("NotResp")) {
                pass = "NULL".equals(resUdsData);
                log.info("fetchCanUdsFunNotResponse:{},检测结果:{},共耗时:{}毫秒", resUdsData, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
            } else {
                if (resUdsData.isEmpty() || expectResult.isEmpty()) {
                    pass = resUdsData.isEmpty() && expectResult.isEmpty();
                } else {
                    pass = compareStrings(resUdsData, expectResult, 'X');
                }
                log.info("获取CAN UDS_FUN响应数据:{},检测结果:{},共耗时:{}毫秒", resUdsData, pass ? "通过" : "失败", System.currentTimeMillis() - startMills);
            }
        } catch (BusError e) {
            log.error(e.getMessage(), e);
        }
        actualExpectedResult.put("fetchCanUdsDataInfo", pass, resUdsData);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_CHECK_REPLY"})
    default ActualExpectedResult checkReply(Integer deviceChannel, String messageId, String byteData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        String resUdsData = checkReplyData(deviceChannel, messageId, byteData);
        boolean pass;
        if (resUdsData.isEmpty() || byteData.isEmpty()) {
            pass = resUdsData.isEmpty() && byteData.isEmpty();
        } else {
            pass = compareStrings(resUdsData, byteData, 'X');
        }
        actualExpectedResult.put("checkReply", pass, resUdsData);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_RESPONSIVE_SERVICES"})
    default ActualExpectedResult responsiveServices(Integer deviceChannel, String requestId, String responseId, String serviceData, String responseData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = responsiveService(deviceChannel, requestId, responseId, serviceData, responseData);
        actualExpectedResult.put("responsiveServices", pass);
        return actualExpectedResult;
    }

    @RegexRule(rule = {"T(com.desaysv.workserver.regex.CanRegexRule.Message).CHANGE_CAN_RESPONSIVE"})
    default ActualExpectedResult responsiveServices(Integer deviceChannel, String serviceData, String responseData) {
        ActualExpectedResult actualExpectedResult = new ActualExpectedResult();
        boolean pass = responsiveService(deviceChannel, "", "", serviceData, responseData);
        actualExpectedResult.put("responsiveServices", pass);
        return actualExpectedResult;
    }

}