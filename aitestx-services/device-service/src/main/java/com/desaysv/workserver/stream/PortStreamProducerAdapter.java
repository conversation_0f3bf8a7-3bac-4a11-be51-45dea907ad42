package com.desaysv.workserver.stream;


import com.desaysv.workserver.constants.DeviceModel;
import com.desaysv.workserver.data_structure.CircularFifoBlockingQueue;
import com.desaysv.workserver.entity.ImageBuffer;
import com.desaysv.workserver.exceptions.image.FrameGrabberException;
import com.desaysv.workserver.model.roi.RectSize;
import com.desaysv.workserver.operation.parameter.CameraSettings;
import com.desaysv.workserver.stream.grabber.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegLogCallback;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.FrameGrabber;

import java.util.Arrays;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * RTSP视频流生产者
 */
@Slf4j
//@Component
//@Scope(value = WebApplicationContext.SCOPE_REQUEST, proxyMode = ScopedProxyMode.TARGET_CLASS)
public abstract class PortStreamProducerAdapter extends PortStreamProducer {

    //FIXME：改成阻塞队列
//    private final CircularFifoBlockingQueue<Frame> shareFrameDeque = new CircularFifoBlockingQueue<>(1);
    private Thread thread;
    private ExecutorService grabExecutor;
    private Runnable runnable;

    //相机采集
    @Setter
    @Getter
    private StreamGrabber grabber;

    private String deviceModel;

    private int devicePort;

    private boolean alive;
    //是否禁用动态输出
    private boolean blockDynamicOutput = false;

    public PortStreamProducerAdapter() {
        super();
    }

    public PortStreamProducerAdapter(String url) {
        super(url);
    }

    public PortStreamProducerAdapter(String url, int w, int h) {
        super(url, w, h);
    }

    public PortStreamProducerAdapter(int w, int h) {
        super(w, h);
    }

    @Override
    public void tryLoad() {
        if (grabber instanceof FrameStreamGrabber) {
            FrameStreamGrabber.tryLoad();
        }
    }

    public void recoverStream() throws FrameGrabber.Exception {
        log.info("恢复视频流采集");
        if (grabber != null) {
            tryLoad();
            grabber.restart();
        }
    }

    public synchronized void close() throws Exception {
        log.info("关闭视频流");
        alive = false;
        if (grabber != null) {
            grabber.close();
        }
//        this.thread.interrupt();
        if (grabExecutor != null) {
            grabExecutor.shutdownNow();
        }
    }

    protected void startGrabber(String deviceModel) throws FrameGrabber.Exception {
        if (grabber != null) {
            log.info("正在启动相机...");
            if (deviceModel.equals(DeviceModel.Camera.HIK_CAMERA)) {
                if (getWidth() > 0 && getHeight() > 0) {
                    log.info("相机设置分辨率:{}x{}", getWidth(), getHeight());
                    grabber.start(getWidth(), getHeight());
                } else {
                    grabber.start();
                }
            } else {
                if (getWidth() > 0 && getHeight() > 0) {
                    log.info("相机设置分辨率:{}x{}", getWidth(), getHeight());
                    grabber.setImageWidth(getWidth());
                    grabber.setImageHeight(getHeight());
                }
                if (getFrameRate() > 0) {
                    log.info("相机设置帧率:{}", getFrameRate());
                    grabber.setFrameRate(getFrameRate());
                }
                grabber.start();
                //TODO:当获取的frame为null，要重新start(),保证持续获取视频帧
            }
            log.info("相机分辨率:{}x{}", grabber.getImageWidth(), grabber.getImageHeight());
            setWidth(grabber.getImageWidth());
            setHeight(grabber.getImageHeight());
            log.info("相机采集开始");
            FFmpegLogCallback.set();
        }
    }


    public void startPushStream(String deviceModel, int devicePort) throws Exception {
        FFmpegLogCallback.set();
        alive = true;
        try {
            startStream(deviceModel, devicePort);
        } catch (Exception e) {
            handleExceptionAndClose(e);
            throw e;
        }
        log.info("推流线程开始");
//        thread.start();
        grabExecutor = Executors.newSingleThreadExecutor();
        grabExecutor.execute(runnable);
    }

    private void prepareStream(Consumer<Frame> consumer) {
        ///推流
        this.runnable = () -> {
            while (alive) {
                try {
                    if (blockDynamicOutput) {
                        synchronized (this) {
                            try {
//                                log.info("图像采集等待中...");
                                wait();
                            } catch (InterruptedException e) {
                                if (!alive) {
                                    log.warn(e.getMessage(), e);
                                }
                            }
                        }
                    }
                    if (alive) {
                        ImageBuffer buffer = grabImageBuffer();
                        // 分发视频流
                        if (buffer.getData() != null) {
                            distributeStream(buffer);
                        }
                        TimeUnit.MILLISECONDS.sleep(10);
                    }
                } catch (Exception e) {
                    if (alive) {
                        handleExceptionAndRecover(e);
                    }
                }
            }
        };
    }

    protected void handleExceptionAndRecover(Exception e) {
        log.error(e.getMessage(), e);
        try {
            recoverStream();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    protected void handleExceptionAndClose(Exception e) {
        log.error(e.getMessage(), e);
        try {
            close();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    /**
     * 捕捉图像buffer（地址相同）
     *
     * @return 图像buffer
     */
    public ImageBuffer grabImageBuffer() throws FrameGrabber.Exception {
        ImageBuffer imageBuffer = grabber.grabByteBuffer();
//        final Frame frame = imageBuffer.getFrame();
//        if (frame != null) {
//            shareFrameDeque.add(frame);
//        }
        return imageBuffer;
    }

    /**
     * 拍摄单帧图像（地址可以不同）
     *
     * @return 图像
     */
    @Override
    public Frame grabFrame() throws FrameGrabberException {
        if (grabber == null) {
            throw new FrameGrabberException("grabber未启动");
        }
        try {
            if (grabber instanceof FrameStreamGrabber) {
                grabber.grab(); //去除之前的缓存
            }
            //            System.out.println("frame:" + System.identityHashCode(frame));
            return grabber.grab();
        } catch (FrameGrabber.Exception e) {
            throw new FrameGrabberException(e);
        }
    }

    /**
     * 控制视频动态或静止
     *
     * @param isOutput: 是否输出动态帧
     */
    public void outputDynamicFrame(boolean isOutput) {
        if (isOutput) {
            if (blockDynamicOutput) {
                log.info("开启动态画面输出");
                blockDynamicOutput = false;
                synchronized (this) {
                    notifyAll();
                }
            }
        } else {
            //禁用动态输出
            log.info("开启静态画面输出");
            blockDynamicOutput = true;
        }
    }

    /**
     * @param cameraSettings: 相机设置
     * @return void
     * <AUTHOR>
     * @description 设置自动曝光模式与上下限
     * @date 2024/7/5 16:23
     */
    public void setExposureAuto(CameraSettings cameraSettings) {
        grabber.setExposureAuto(cameraSettings.getAutoExposureMode(),
                cameraSettings.getAutoExposureTimeLower(),
                cameraSettings.getAutoExposureTimeUpper());
    }

    public void setReverseX(boolean reverseX) {
        grabber.setReverseX(reverseX);
    }

    public void setReverseY(boolean reverseY) {
        grabber.setReverseY(reverseY);
    }

    public CameraSettings getCameraSettings() {
        return grabber.getCameraSettings(new CameraSettings());
    }

    public void grabberSwitch(boolean status) {
        outputDynamicFrame(status);
        grabber.grabberSwitch(status);
    }


    @Override
    public RectSize getDefaultSize() {
        if (grabber != null) {
            return new RectSize(grabber.getImageWidth(), grabber.getImageHeight());
        }
        return null;
    }

    /**
     * 推送相机视频流到rtsp服务器
     *
     * @param deviceModel 相机型号
     * @param devicePort  相机序号
     * @return rtsp url
     * @throws Exception 异常
     */
    public String pushStream(String deviceModel, int devicePort) throws Exception {
//        FrameGrabber grabber = new OpenCVFrameGrabber(deviceNumber);
        this.deviceModel = deviceModel;
        this.devicePort = devicePort;
        StreamGrabber streamGrabber = initStreamGrabber();
        setGrabber(streamGrabber);
        prepareStream(System.out::println);
        startPushStream(deviceModel, devicePort);
        return getUrl();
    }

    public StreamGrabber initStreamGrabber() {
        StreamGrabber streamGrabber;
        if (deviceModel.equals(DeviceModel.Camera.HIK_CAMERA)) {
            streamGrabber = new HikStreamGrabber(devicePort);
        } else if (DeviceModel.Utils.contains(DeviceModel.Android.class, deviceModel)) {
            streamGrabber = new AndroidStreamGrabber();
        } else if (DeviceModel.Utils.contains(DeviceModel.VideoCapture.class, deviceModel)) {
            streamGrabber = new VideoCaptureStreamGrabber();
        } else {
            streamGrabber = new FrameStreamGrabber(devicePort);
        }
        return streamGrabber;
    }

    @Override
    public void closeStream() {
        try {
            close();
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    public double getGrabberFrameRate() {
        return grabber.getFrameRate() == 0.0 ? 30.0 : grabber.getFrameRate();
    }

    public static void main(String[] args) {
        CircularFifoBlockingQueue<Integer> shareFrameDeque = new CircularFifoBlockingQueue<>(2);
        System.out.println(shareFrameDeque.add(1));
        System.out.println(shareFrameDeque.add(22));
        System.out.println(shareFrameDeque.add(333));
        System.out.println(Arrays.toString(shareFrameDeque.toArray()));

    }
}
