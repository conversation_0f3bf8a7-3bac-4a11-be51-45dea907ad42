package com.desaysv.workserver.devices.qnx;

import com.desaysv.workserver.entity.ConfigurableDevice;
import com.desaysv.workserver.operation.parameter.DeviceOperationParameter;

public abstract class QnxDevice extends ConfigurableDevice<QnxDeviceConfig> implements IQnxDevice {

    public QnxDevice() {
        this(new DeviceOperationParameter());
    }

    public QnxDevice(DeviceOperationParameter deviceOperationParameter) {

    }
}
