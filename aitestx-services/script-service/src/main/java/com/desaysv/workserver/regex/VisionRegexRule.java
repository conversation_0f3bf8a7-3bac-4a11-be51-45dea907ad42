package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

import static com.desaysv.workserver.action_sequence.BaseRegexRule.RegexRuleConstants.*;

/**
 * 图像识别动作序列
 */
public class VisionRegexRule extends BaseRegexRule {
    private final static String PATTERN = wholeMatchCaseInsensitive("Pattern");
    private final static String MODEL = wholeMatchCaseInsensitive("Model");
    private final static String NO_EXIST = wholeMatchCaseInsensitive("NoExist");
    private final static String FREQUENCY = wholeMatchCaseInsensitive("Frequency");
    private final static String LIGHT = wholeMatchCaseInsensitive("Light");
    private final static String DARK = wholeMatchCaseInsensitive("Dark");
    private final static String PHOTO = wholeMatchCaseInsensitive("Photo");
    private final static String VIDEO = wholeMatchCaseInsensitive("Video");


    /**
     * 功能：模板识别
     * 格式：Vision-Pattern-X0(模板名)-X1(相似度最小百分比)-X2(图像识别算法)
     */
    public static final String PATTERN_RECOGNITION_ALL = wholeCombine(PATTERN, group(WORD), group(add(NUMBER, "%")), group(PARAM));

    /**
     * 功能：模板识别
     * 格式：Vision-Pattern-X0(模板名)-X1(相似度最小百分比)
     */
    public static final String PATTERN_RECOGNITION_THRESHOLD = wholeCombine(PATTERN, group(WORD), group(add(NUMBER, "%")));

    /**
     * 功能：模型识别
     * 格式：Vision-Model-Light-X0(模板名)-X1(相似度最小百分比)
     */
    public static final String MODEL_RECOGNITION_LIGHT_THRESHOLD = wholeCombine(MODEL, LIGHT, group(WORD), group(add(NUMBER, "%")));

    /**
     * 功能：模型识别
     * 格式：Vision-Model-Dark-X0(模板名)-X1(相似度最小百分比)
     */
    public static final String MODEL_RECOGNITION_DARK_THRESHOLD = wholeCombine(MODEL, DARK, group(WORD), group(add(NUMBER, "%")));

    /**
     * 功能：模板识别
     * 格式：Vision-Pattern-X0(模板名)-X1(图像识别算法)
     */
    public static final String PATTERN_RECOGNITION_ALGORITHM = wholeCombine(PATTERN, group(WORD), group(PARAM));

    /**
     * 功能：模板识别
     * 格式：Vision-Pattern-X0(模板名)
     */
    public static final String PATTERN_RECOGNITION = wholeCombine(PATTERN, group(WORD));

    /**
     * 功能：模型识别
     * 格式：Vision-Model-Light-X0(模板名)
     */
    public static final String MODEL_RECOGNITION_LIGHT = wholeCombine(MODEL, LIGHT, group(WORD));

    /**
     * 功能：模型识别
     * 格式：Vision-Model-Dark-X0(模板名)
     */
    public static final String MODEL_RECOGNITION_DARK = wholeCombine(MODEL, DARK, group(WORD));

    /**
     * 功能：模板识别（反向不存在）
     * 格式：Vision-Pattern-NoExist-X0(模板名)
     */
    public static final String PATTERN_RECOGNITION_NO_EXIST = wholeCombine(PATTERN, NO_EXIST, group(WORD));

    /**
     * 功能：模板识别（反向不存在）
     * 格式：Vision-Pattern-NoExist-X0(模板名)-X1(相似度最小百分比)
     */
    public static final String PATTERN_RECOGNITION_NO_EXIST_THRESHOLD = wholeCombine(PATTERN, NO_EXIST, group(WORD), group(add(NUMBER, "%")));

    // OCR识别

    /**
     * 功能：OCR字符识别（NA）——旧序列
     * 格式：Vision-OCR-X0(模板名)-NA-X1(任意字符)
     */
    public static final String OCR_CHARA_RECOGNITION_FOR_NA = wholeCombine(OCR, group(WORD), NA, group(WORD_SPACE_LINEBREAK_MH));

    /**
     * 功能：OCR字符识别（TEXT）
     * 格式：Vision-Pattern-X0(模板名)-OCR-X1(任意字符)
     */
    public static final String OCR_CHAR_RECOGNITION = wholeCombine(PATTERN, group(WORD), OCR, group(WORD_SPACE_LINEBREAK_MH));

    /**
     * 功能：OCR字符识别（TEXT）
     * 格式：Vision-Pattern-NoExist-X0(模板名)-OCR-X1(任意字符)
     */
    public static final String OCR_CHAR_RECOGNITION_NOT_EXIST = wholeCombine(PATTERN, NO_EXIST, group(WORD), OCR, group(WORD_SPACE_LINEBREAK_MH));

    /**
     * 功能：OCR字符识别（TEXT）
     * 格式：Vision-Pattern-X0(模板名)-OCR-X1(任意字符)-X2(相似度最小百分比)
     */
    public static final String OCR_CHAR_RECOGNITION_THRESHOLD = wholeCombine(PATTERN, group(WORD), OCR, group(WORD_SPACE_LINEBREAK_MH), group(add(NUMBER, "%")));

    /**
     * 功能：OCR字符识别（TEXT）
     * 格式：Vision-Pattern-NoExist-X0(模板名)-OCR-X1(任意字符)-X2(相似度最小百分比)
     */
    public static final String OCR_CHAR_RECOGNITION_THRESHOLD_NOT_EXIST = wholeCombine(PATTERN, NO_EXIST, group(WORD), OCR, group(WORD_SPACE_LINEBREAK_MH), group(add(NUMBER, "%")));

    /**
     * 功能：OCR数值识别（NUMBER）
     * 格式：Vision-Pattern-X0(模板名)-NO-X1(数值)-X2(相似度最小百分比)
     */
    public static final String OCR_NUMBER_RECOGNITION = wholeCombine(PATTERN, group(WORD), NO, group(NUMBER), group(add(NUMBER, "%")));

    /**
     * 功能：OCR数值范围识别（NUMBER）
     * 格式：Vision-Pattern-X0(模板名)-NO-X1(数值下限)-X2(数值上限)-X3(相似度最小百分比)
     */
    public static final String OCR_NUMBER_RANGE_RECOGNITION = wholeCombine(PATTERN, group(WORD), NO, group(NUMBER), group(NUMBER), group(add(NUMBER, "%")));

    /**
     * 功能：OCR时间识别（TIME）
     * 格式：Vision-Pattern-X0(模板名)-TIME-X1(相似度最小百分比)
     */
    public static final String OCR_TIME_RECOGNITION = wholeCombine(PATTERN, group(WORD), TIME, group(ANY_TIME), group(add(NUMBER, "%")));

    /**
     * 功能：OCR字符识别（识别字符和模板名一样）
     * 格式：Vision-OCR-X1(识别字符/模板名)
     */
    public static final String OCR_TEMPLATE_NAME_RECOGNITION = wholeCombine(OCR, group(WORD));

    /**
     * 功能：OCR字符识别（识别字符和模板名一样）
     * 格式：Vision-OCR-X1(识别字符/模板名)-X2(相似度最小百分比)
     */
    public static final String OCR_TEMPLATE_NAME_RECOGNITION_THRESHOLD = wholeCombine(OCR, group(WORD), group(add(NUMBER, "%")));

    /**
     * 功能：OCR字符识别（识别字符和模板名一样，反向不存在）
     * 格式：Vision-OCR-NoExist-X1(识别字符/模板名)
     */
    public static final String OCR_NO_EXIST_TEMPLATE_NAME_RECOGNITION = wholeCombine(OCR, NO_EXIST, group(WORD));

    /**
     * 功能：OCR字符识别（识别字符和模板名一样，反向不存在）
     * 格式：Vision-OCR-NoExist-X1(识别字符/模板名)-X2(相似度最小百分比)
     */
    public static final String OCR_NO_EXIST_TEMPLATE_NAME_RECOGNITION_THRESHOLD = wholeCombine(OCR, NO_EXIST, group(WORD), group(add(NUMBER, "%")));


    // 闪烁识别

    /**
     * 功能：闪烁识别
     * 格式：Vision-Pattern-X0(模板名)-Frequency
     */
    public static final String PATTERN_RECOGNITION_LIGHT_BLINKING = wholeCombine(PATTERN, group(WORD), FREQUENCY);

    /**
     * 功能：闪烁识别
     * 格式：Vision-Pattern-X0(模板名)-Frequency-X1(频率起始值)
     */
    public static final String PATTERN_RECOGNITION_LIGHT_BLINKING_START_VALUE = wholeCombine(PATTERN, group(WORD), FREQUENCY, group(NUMBER));

    /**
     * 功能：闪烁识别
     * 格式：Vision-Pattern-X0(模板名)-Frequency-X1(频率起始值)-X2(频率终止值)
     */
    public static final String PATTERN_RECOGNITION_LIGHT_BLINKING_ALL = wholeCombine(PATTERN, group(WORD), FREQUENCY, group(NUMBER), group(NUMBER));

    /**
     * 功能：闪烁识别
     * 格式：Vision-Pattern-X0(模板名)-Frequency-X1(频率起始值)-X2(频率终止值)-X3(相似度最小百分比)
     */
    public static final String PATTERN_RECOGNITION_LIGHT_BLINKING_RATIO = wholeCombine(PATTERN, group(WORD), FREQUENCY, group(NUMBER), group(NUMBER), group(add(NUMBER, "%")));

    /**
     * 功能：闪烁识别
     * 格式：Vision-Pattern-X0(模板名)-Frequency-X1(频率起始值)-X2(频率终止值)-X3(亮与不亮阈值最小百分比)-X3(相似度最小百分比)-X4(图像识别算法)
     */
    public static final String PATTERN_RECOGNITION_LIGHT_BLINKING_ALGORITHM = wholeCombine(PATTERN, group(WORD), FREQUENCY, group(NUMBER), group(NUMBER), group(add(NUMBER, "%")), group(add(NUMBER, "%")), group(PARAM));

    /**
     * 功能：拍照
     * 格式：Vision-Photo-X0(caseID保存文件夹名)
     */
    public static final String TAKE_PHOTO = wholeCombine(PHOTO);

    /**
     * 功能：开始录像
     * 格式：Vision-Video-start/stop-X0(caseID保存文件夹名)
     */
    public static final String VIDEO_RECORDING = wholeCombine(VIDEO, group(START_STOP));

}
