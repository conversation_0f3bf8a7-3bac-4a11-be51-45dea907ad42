package com.desaysv.workserver.finder;

import com.desaysv.workserver.base.operation.targets.OperationTarget;
import com.desaysv.workserver.components.SequenceDeviceKeywords;
import com.desaysv.workserver.constants.DeviceType;
import com.desaysv.workserver.devices.android.IAndroid;
import com.desaysv.workserver.devices.autoclicker.IAutoClicker;
import com.desaysv.workserver.devices.bus.interfaces.ICanSequenceAll;
import com.desaysv.workserver.devices.bus.interfaces.IEthernetSequence;
import com.desaysv.workserver.devices.bus.interfaces.ILinSequence;
import com.desaysv.workserver.devices.camera.base.IVisionDevice;
import com.desaysv.workserver.devices.daq.base.IDaqCompare;
import com.desaysv.workserver.devices.device_tcp_client.interfaces.ITcpClientDevice;
import com.desaysv.workserver.devices.device_udp.interfaces.IDeviceUDP;
import com.desaysv.workserver.devices.electronic_load.base.interfaces.IElectronicLoad;
import com.desaysv.workserver.devices.oscilloscope.interfaces.IOscilloscope;
import com.desaysv.workserver.devices.power.base.interfaces.IPowerDevice;
import com.desaysv.workserver.devices.qnx.IQnxDevice;
import com.desaysv.workserver.devices.robot.interfaces.IRobotDevice;
import com.desaysv.workserver.devices.serial.interfaces.ISerial;
import com.desaysv.workserver.devices.signal_generator.interfaces.ISignalGenerator;
import com.desaysv.workserver.devices.soundcard.interfaces.ISound;
import com.desaysv.workserver.devices.tcpserver.interfaces.ITcpServer;
import com.desaysv.workserver.devices.testbox.all.*;
import com.desaysv.workserver.devices.testbox.interfaces.rzcu.IRZCUDcCollector;
import com.desaysv.workserver.virtual.AdsTool;
import lombok.Data;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> lwj
 * @description : 动作序列操作目标查找器管理器
 */
public class OperationTargetFinderManager {

    @Data
    public static class SequenceDevice {
        private List<String> deviceTypes;
        private boolean virtual; //是否是虚拟设备
        private Class<?> clazz; //操作目标类

        public SequenceDevice(List<String> deviceTypes) {
            this(deviceTypes, false);
        }

        public SequenceDevice(List<String> deviceTypes, Class<?> clazz) {
            this(deviceTypes, false, clazz);
        }

        public SequenceDevice(List<String> deviceTypes, boolean virtual) {
            this(deviceTypes, virtual, null);
        }

        public SequenceDevice(List<String> deviceTypes, boolean virtual, Class<?> clazz) {
            this.deviceTypes = deviceTypes;
            this.virtual = virtual;
            this.clazz = clazz;
        }
    }

    public static final Map<String, OperationTarget> virtualOperationTargetMap = new HashMap<String, OperationTarget>() {
        {
            put(DeviceType.COMMON_VIRTUAL, AdsTool.getInstance());
        }
    };

    public static final Map<String, SequenceDevice> finderDeviceTypeMap = new HashMap<String, SequenceDevice>() {
        {
            put(SequenceDeviceKeywords.RELAY_BOARD, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX, DeviceType.DEVICE_ELECTRIC_RELAY), IRelaySwitchBoardAll.class));
            put(SequenceDeviceKeywords.RESISTOR, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX, DeviceType.DEVICE_RESISTANCE), IResistorBoardAll.class));
            put(SequenceDeviceKeywords.PWM_OUT, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX), IPwmOutBoardAll.class));
            put(SequenceDeviceKeywords.PWM_IN, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX), IPwmInBoardAll.class));
            put(SequenceDeviceKeywords.COLLECT_VOLTAGE, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX), IVoltageAcquireBoardAll.class));
            put(SequenceDeviceKeywords.TRI_STATE_OUTPUT, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX), ITriStateOutputBoardAll.class));
            put(SequenceDeviceKeywords.DC_COLLECTOR, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TEST_BOX), IRZCUDcCollector.class));
            put(SequenceDeviceKeywords.DAQ, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_DAQ), IDaqCompare.class));
            put(SequenceDeviceKeywords.ADB, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_ANDROID), IAndroid.class));
            put(SequenceDeviceKeywords.POWER, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_POWER), IPowerDevice.class));
            put(SequenceDeviceKeywords.ROBOT, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_ROBOT), IRobotDevice.class));
            put(SequenceDeviceKeywords.CAN, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_CAN), ICanSequenceAll.class));
            put(SequenceDeviceKeywords.LIN, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_LIN), ILinSequence.class));
            put(SequenceDeviceKeywords.ETHERNET, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_ETHERNET), IEthernetSequence.class));
            put(SequenceDeviceKeywords.VISION, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_CAMERA), IVisionDevice.class));
            put(SequenceDeviceKeywords.CAPTURE, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_VIDEO_CAPTURE), IVisionDevice.class));//增加视频采集仪的指令
            put(SequenceDeviceKeywords.SERIAL, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_SERIAL), ISerial.class));
            put(SequenceDeviceKeywords.ADS_TOOL, new SequenceDevice(Arrays.asList(DeviceType.COMMON_VIRTUAL), true, AdsTool.class));
            put(SequenceDeviceKeywords.QNX, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_QNX), IQnxDevice.class));
            put(SequenceDeviceKeywords.SOUND, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_SOUND_CARD), ISound.class));
            put(SequenceDeviceKeywords.UDP, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_UDP), IDeviceUDP.class));
            put(SequenceDeviceKeywords.TCP, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TCP_CLIENT), ITcpClientDevice.class));
            put(SequenceDeviceKeywords.ELE_LOAD, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_ELECTRONIC_LOAD), IElectronicLoad.class));
            put(SequenceDeviceKeywords.OSCILLOSCOPE, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_OSCILLOSCOPE), IOscilloscope.class));
            put(SequenceDeviceKeywords.SIGNAL_GENERATOR, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_SIGNAL_GENERATOR), ISignalGenerator.class));
            put(SequenceDeviceKeywords.TCP_SERVER, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_TCP_SERVER), ITcpServer.class));
            put(SequenceDeviceKeywords.AUTO_CLICKER, new SequenceDevice(Arrays.asList(DeviceType.DEVICE_AUTO_CLICKER), IAutoClicker.class));
        }
    };


}
