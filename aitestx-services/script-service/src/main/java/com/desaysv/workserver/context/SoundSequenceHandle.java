package com.desaysv.workserver.context;

import com.desaysv.workserver.components.ActionSequence;
import com.desaysv.workserver.parser.ActionSequenceParserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SoundSequenceHandle {

    private static final Pattern CHECK_LINE_PATTERN = Pattern.compile("-?Check-\\d+", Pattern.CASE_INSENSITIVE);
    private static final Pattern CHECK_ALL_LINE_PATTERN = Pattern.compile("-?Check-all+", Pattern.CASE_INSENSITIVE);
    private static final Pattern Sound_LINE_PATTERN = Pattern.compile(".*-AMP-.*", Pattern.CASE_INSENSITIVE);
    private static final Pattern Sound_Record_LINE_PATTERN = Pattern.compile("-?Sound-Record-.*", Pattern.CASE_INSENSITIVE);

    @Autowired
    private ActionSequenceParserService actionSequenceParserService;
    /**
     * 增加对声音处理
     * 前后增加 Sound-0-Record-ON，Sound-0-Record-OFF-check-1
     *
     * @return
     */
    public void addSoundStepsForChecks(ActionSequenceContext context) {
        List<TestStep> expectResult = context.getExpectResult();
        List<TestStep> operationStep = context.getOperationStep();
        if (expectResult == null) return ;
        boolean containsSound = expectResult.stream()
                .anyMatch(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                        Sound_LINE_PATTERN.matcher(testStep.getTestStep()).find());
        if (!containsSound) {
            return ;
        }
        // 提取 expectResult 中第一个匹配 "Sound-数字" 的通道号
        Integer chanel = expectResult.stream()
                // 筛选出非空且匹配 Sound_LINE_PATTERN 的步骤
                .filter(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                        Sound_LINE_PATTERN.matcher(testStep.getTestStep()).find())
                // 提取第一个匹配的步骤
                .findFirst()
                .map(testStep -> {
                    String testStepValue = testStep.getTestStep();
                    //匹配"-AMP"前的第一个数字
                    Matcher matcher = Pattern.compile("(?<=-)(\\d+)(?=-AMP-)", Pattern.CASE_INSENSITIVE).matcher(testStepValue);
                    if (matcher.find()) {
                        try {
                            // 提取并返回数字
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            // 如果解析失败，默认通道为 1
                            return 1;
                        }
                    }
                    return 1; // 如果没有找到匹配的部分，默认通道为 1
                })
                .orElse(1); // 如果 Optional 为空，默认通道为 1
        Integer deviceId = expectResult.stream()
                // 筛选出非空且匹配 Sound_LINE_PATTERN 的步骤
                .filter(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                        Sound_LINE_PATTERN.matcher(testStep.getTestStep()).find())
                // 提取第一个匹配的步骤
                .findFirst()
                .map(testStep -> {
                    String testStepValue = testStep.getTestStep();
                    //匹配 "Sound#"和"-"之间的第一个数字
                    Matcher matcher = Pattern.compile("Sound#(\\d+)-", Pattern.CASE_INSENSITIVE).matcher(testStepValue);
                    if (matcher.find()) {
                        try {
                            // 提取并返回数字
                            return Integer.parseInt(matcher.group(1));
                        } catch (NumberFormatException e) {
                            // 如果解析失败，默认通道为 1
                            return 1;
                        }
                    }
                    return 1; // 如果没有找到匹配的部分，默认通道为 1
                })
                .orElse(1); // 如果 Optional 为空，默认通道为 1;
        TestStep recordOnStep = new TestStep();
        recordOnStep.setTestStep("Sound#" + deviceId + "-" + chanel + "-Record-On");

        TestStep waitTimeStep = new TestStep();
        waitTimeStep.setTestStep("Wait-2000ms");

        TestStep recordOffStep = new TestStep();
        recordOffStep.setTestStep("Sound#" + deviceId + "-" + chanel + "-Record-Off");
        //期望序列有声音断言，但是动作序列为空没有录制步骤，则在动作序列首尾位置添加录制步骤
        if (operationStep == null) {
            operationStep = new ArrayList<>();
            operationStep.add(recordOnStep);
//            operationStep.add(waitTimeStep);
            operationStep.add(waitTimeStep);
            operationStep.add(recordOffStep);
            return ;
        } else {
            boolean containsRecord = operationStep.stream()
                    .anyMatch(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                            Sound_Record_LINE_PATTERN.matcher(testStep.getTestStep()).find());
            if (containsRecord) {
                return ;
            }
            boolean containsCheck = operationStep.stream()
                    .anyMatch(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                            CHECK_LINE_PATTERN.matcher(testStep.getTestStep()).find());
            boolean containsCheckAll = operationStep.stream()
                    .anyMatch(testStep -> StringUtils.isNotEmpty(testStep.getTestStep()) &&
                            CHECK_ALL_LINE_PATTERN.matcher(testStep.getTestStep()).find());
            if (!containsCheck || containsCheckAll) {
                //期望序列有声音断言，动作序列不为空但是没有录制步骤，不包含check或者包含checkALL的情况则在动作序列尾部位置添加录制步骤
                operationStep.add(recordOnStep);
                operationStep.add(waitTimeStep);
//                operationStep.add(waitTimeStep);
                operationStep.add(recordOffStep);
                return ;
            }
        }
        //有声音断言，且有对应check但是没有录音的情况，就针对每个check的operation后添加录音
        for (int i = 0; i < operationStep.size(); i++) {
            TestStep step = operationStep.get(i);
            String testStepStr = step.getTestStep();

            // 检查步骤是否包含 "check" 且不包含 "record"（避免重复增加 on/off）
            if (testStepStr.trim().toLowerCase().contains("check") && !testStepStr.toLowerCase().contains("record")) {
                // 使用正则表达式提取 "-check-" 后面的序号
                String checkPartStr = testStepStr.replaceAll("(?i).*?(-Check-)\\s*(.*?)\\s*$", "$2")
                        .replaceAll("\\s+", "");
                List<ActionSequence> actionSequences = actionSequenceParserService.parseActionSequenceList(expectResult);
                //遍历actionSequences，找到其中userSequenceOrder和checkPartStr相同的ActionSequence
                ActionSequence checkedActionSequence = actionSequences.stream()
                        .filter(s -> s.getUserSequenceOrder().equals(checkPartStr))
                        .findFirst()
                        .orElse(null);
                if (checkedActionSequence == null) continue;
                //找到expectResult中的TestStep的uuid和checkedActionSequence的uuid一样的TestStep
                TestStep checkStep = expectResult.stream()
                        .filter(s -> s.getUuid().equals(checkedActionSequence.getTestStepUUID()))
                        .findFirst()
                        .orElse(null);
                if (checkStep == null) continue;
                if (checkStep.getTestStep().toLowerCase().contains("amp")) {
                    // 获取原步骤对象
                    TestStep updatedStep = operationStep.get(i);
                    String updatedTestStepStr = updatedStep.getTestStep();

                    // 提取check部分
                    Pattern pattern = Pattern.compile("-Check-.*", Pattern.CASE_INSENSITIVE);
                    Matcher matcher = pattern.matcher(updatedTestStepStr);
                    if (matcher.find()) {
                        // 获取被替换的部分
                        String replacedPart = matcher.group();
                        // 更新原步骤，去掉check部分
                        String newStepStr = updatedTestStepStr.replaceFirst("(?i)-check-.*", "").trim();
                        updatedStep.setTestStep(newStepStr);

                        // 创建录音结束步骤，并添加原来的check部分
                        TestStep soundOffStep = new TestStep();
                        soundOffStep.setTestStep("Sound#" + deviceId + "-" + chanel + "-Record-Off" + replacedPart);
                        soundOffStep.setType(updatedStep.getType());

                        // 插入录音开始步骤、等待和录音结束步骤
                        operationStep.add(i + 1, recordOnStep);
                        operationStep.add(i + 2, waitTimeStep);
//                        operationStep.add(i + 3, waitTimeStep);
                        operationStep.add(i + 3, soundOffStep);
                        i += 3; // 继续调整索引
                    }
                }
            }
        }
    }

}
