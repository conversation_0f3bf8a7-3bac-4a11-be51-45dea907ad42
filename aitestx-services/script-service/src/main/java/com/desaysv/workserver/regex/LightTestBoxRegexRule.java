package com.desaysv.workserver.regex;

import com.desaysv.workserver.action_sequence.BaseRegexRule;

public class LightTestBoxRegexRule extends BaseRegexRule {

    //2.PwmOut-1-2-1-0-10hz-50%
    public static final String CHANGE_LIGHT_PWM_OUT_VALUE = wholeCombine(group(NUMBER), group(NUMBER), group(NUMBER), RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT);

    //1.VoltOut-1-4-1-0
    public static final String CHANGE_LIGHT_HL_OUTPUT_VALUE = wholeCombine(group(NUMBER), group(NUMBER), group(NUMBER));

    //1.VoltOut-1-4-1-0-100ms-1
    public static final String CHANGE_LIGHT_HL_OUTPUT_VALUE_WITH_TIME = wholeCombine(group(NUMBER), group(NUMBER), group(NUMBER), group(ANY_TIME), group(NUMBER));

    //1.PWMGet-1-3-1-12hz-10%-50%-10%
    //频率-频率偏差-占空比-占空比偏差
    public static final String LIGHT_PWM_IN_ACQUIRE = wholeCombine(group(NUMBER), group(NUMBER), RegexRuleConstants.FREQUENCY, RegexRuleConstants.PERCENT,
            RegexRuleConstants.PERCENT, RegexRuleConstants.PERCENT);

    //1.VoltGet-1-2-1-12V-10%
    public static final String LIGHT_VOLTAGE_ACQUIRE = wholeCombine(group(NUMBER), group(NUMBER), RegexRuleConstants.VOLTAGE, RegexRuleConstants.PERCENT);

    //1.VoltGet-1-2-1-12V-15V
    public static final String LIGHT_VOLTAGE_ACQUIRE_RANGE = wholeCombine(group(NUMBER), group(NUMBER), RegexRuleConstants.VOLTAGE, RegexRuleConstants.VOLTAGE);

    /**
     * 1.Switch-1-2-1-ON（带机箱号-卡槽号-通道号，不带注释）
     */
    public static final String SWITCH_ON_OFF = wholeCombine(group(NUMBER), group(NUMBER), group(RegexRuleConstants.ON_OFF));

}
