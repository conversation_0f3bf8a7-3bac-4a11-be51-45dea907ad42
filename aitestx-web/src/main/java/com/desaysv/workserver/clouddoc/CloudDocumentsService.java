package com.desaysv.workserver.clouddoc;

import com.desaysv.workserver.base.manager.ClientInfoObserver;
import com.desaysv.workserver.base.manager.ClientInfoReceiveFromClient;
import com.desaysv.workserver.controller.cicd.AutomaticUpgradeController;
import com.desaysv.workserver.entity.UpgradeResultReportDto;
import com.desaysv.workserver.mail.TestResult;
import com.desaysv.workserver.mail.TestResultReportDto;
import com.desaysv.workserver.monitor.PolyTestUsageStatusUpdater;
import com.desaysv.workserver.monitor.SmokingTestConfigModel;
import com.desaysv.workserver.monitor.SmokingTestConfigObserver;
import com.desaysv.workserver.robot.RobotReport;
import com.desaysv.workserver.robot.RobotService;
import com.desaysv.workserver.utils.DateUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.JsonParser;
import com.lark.oapi.core.request.RequestOptions;
import com.lark.oapi.core.utils.Jsons;
import com.lark.oapi.service.bitable.v1.model.AppTableRecord;
import com.lark.oapi.service.bitable.v1.model.CreateAppTableRecordReq;
import com.lark.oapi.service.bitable.v1.model.CreateAppTableRecordResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.lark.oapi.Client;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Service
public class CloudDocumentsService implements ClientInfoObserver, SmokingTestConfigObserver {

    @Autowired
    private RestTemplate restTemplate;
    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private RobotService robotService;
    private RobotReport cloudDocReport;
    private static final String clientId = "cli_a8aff39a1a34d013";
    private static final String clientSecret = "we4mW0ohGBJFLlkPnFmtnh5gsB7mqq6R";
    private ClientInfoReceiveFromClient clientInfoReceiveFromClient;
    private SmokingTestConfigModel smokingTestConfigModel;
    @Autowired
    private PolyTestUsageStatusUpdater polyTestUsageStatusUpdater;
    @Autowired
    private AutomaticUpgradeController automaticUpgradeController;

    @PostConstruct
    private void init() {
        polyTestUsageStatusUpdater.addObserver(this);
        automaticUpgradeController.addObserver(this);

    }

    @Override
    public void update(ClientInfoReceiveFromClient clientInfo) {
        this.clientInfoReceiveFromClient = clientInfo;
    }

    @Override
    public void update(SmokingTestConfigModel configModel) {
        this.smokingTestConfigModel = configModel;
    }

    /**
     * 获取飞书云文档的访问令牌
     *
     * @return 访问令牌
     */
    public String getTenantAccessToken() {
        // 1. 定义 API 地址
        String url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";

        // 2. 构建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON); // 关键：设置 Content-Type

        // 3. 构建 JSON 请求体
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("app_id", clientId);
        bodyMap.put("app_secret", clientSecret);

        // 4. 封装请求实体
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(bodyMap, headers);

        // 5. 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 6. 发送 POST 请求并解析响应
        ResponseEntity<Map> response = restTemplate.postForEntity(
                url,
                requestEntity,
                Map.class
        );

        // 7. 处理响应
        if (response.getStatusCode() == HttpStatus.OK) {
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && (Integer) responseBody.get("code") == 0) {
                return (String) responseBody.get("tenant_access_token");
            } else {
                throw new RuntimeException("获取 Token 失败: " + responseBody.get("msg"));
            }
        } else {
            throw new RuntimeException("请求失败，状态码: " + response.getStatusCode());
        }
    }

    /**
     * 获取表格的appToken和tableId
     * @return
     */
    public Map<String, String> getAppTokenAndTableId(){
        //获取机器人地址
        List<String> urlsList = new ArrayList<>(Arrays.asList(clientInfoReceiveFromClient.getMultiTableUrls()));
        // 正则表达式，用于匹配 appToken 和 tableId
        Pattern pattern = Pattern.compile("/base/(\\w+)\\?table=(\\w+)");

        // 存储结果的 map
        Map<String, String> tableToAppTokenMap = new HashMap<>();

        for (String url : urlsList) {
            Matcher matcher = pattern.matcher(url);
            if (matcher.find()) {
                String appToken = matcher.group(1); // 第一个捕获组：appToken
                String tableId = matcher.group(2); // 第二个捕获组：tableId

                tableToAppTokenMap.put(tableId, appToken);
            }
        }

        // 输出结果
        for (Map.Entry<String, String> entry : tableToAppTokenMap.entrySet()) {
            log.info("tableId: {}, appToken: {}", entry.getKey(), entry.getValue());
        }
        return tableToAppTokenMap;
    }


    /**
     * 添加测试结果数据到飞书表格
     *
     * @return true/false
     */
    public boolean addNewDataToTestResultTable(TestResultReportDto cloudDocReportDto){
        // 构建client
        Client client = Client.newBuilder(clientId, clientSecret).build();
        Date versionDate = new Date();
        String[] testUnitArr = clientInfoReceiveFromClient.getTestUnit().split("/");
        String recordId = "";
        String versionStr = "";
        boolean getVersionSuccess = false;
        if (!cloudDocReportDto.isFunctionMode()){
            versionStr = cloudDocReportDto.getVersion(); // 获取版本日期字符串，例如 "20250603"
        }else {
            versionStr = smokingTestConfigModel.getVersion(); // 获取版本日期字符串，例如 "20250603"
        }
        if (!versionStr.isEmpty()){
            try  {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                versionDate = sdf.parse(versionStr);// 转换为 Date 对象
                getVersionSuccess = true;
                log.info("版本日期转换成功: {}", versionDate);
            } catch (Exception e) {
                log.error("日期转换异常: {}", e.getMessage());
            }
        }
        //结果整理
        cloudDocReport = cloudDocReportDto.isFunctionMode()? robotService.getFunctionRobotReport(cloudDocReportDto) : robotService.getSmokeRobotReport(cloudDocReportDto);
        Map < String, String > tableToAppTokenMap = getAppTokenAndTableId();
        if (tableToAppTokenMap.isEmpty()){
            log.error("多维表格地址为空！！请先添加多维表格");
            return false;
        }
        for (Map.Entry<String, String> entry : tableToAppTokenMap.entrySet()) {
            // 创建请求对象
            Date finalVersionDate = versionDate;
            boolean finalGetVersionSuccess = getVersionSuccess;
            CreateAppTableRecordReq req = CreateAppTableRecordReq.newBuilder()
                    .appToken(entry.getValue())
                    .tableId(entry.getKey())
                    .appTableRecord(AppTableRecord.newBuilder()
                            .fields(new HashMap < String, Object > () {
                                {
                                    // 映射字段  数据处理，分为 功能测试和冒烟测试，所以需要判断
                                    put("项目名称", cloudDocReport.getProjectName());
                                    put("测试类型", cloudDocReportDto.isFunctionMode()? "功能测试" : "冒烟测试");
//                                    put("测试模块", cloudDocReport.getModuleNameSummary());
                                    put("测试模块", cloudDocReportDto.isFunctionMode()? "全功能" : "冒烟");
                                    if (finalGetVersionSuccess){
                                        put("软件集成版本日期", finalVersionDate.getTime());
                                    }
                                    put("SOC版本信息", cloudDocReport.getSoc());
                                    put("MCU版本信息", cloudDocReport.getMcu());
                                    put("QNX版本信息", cloudDocReport.getQnx());
                                    put("开始测试时间", cloudDocReport.getStartTime().getTime());
                                    put("结束测试时间", cloudDocReport.getEndTime().getTime());
                                    put("测试台架名称", testUnitArr[0]);//主机名
                                    put("测试案例总数", cloudDocReport.getCaseTotal());
                                    put("通过案例数", cloudDocReport.getPassTotal());
                                    put("未通过案例数", cloudDocReport.getFailTotal());
                                    put("通过率", cloudDocReport.getPassRate());
                                    put("是否通过", cloudDocReport.getResult());
                                    put("测试报告路径", cloudDocReport.getReportPath());
                                }
                            })
                            .build())
                    .build();

            // 发起请求
            try {
                CreateAppTableRecordResp resp = client.bitable().v1().appTableRecord().create(req, RequestOptions.newBuilder()
                        .tenantAccessToken(getTenantAccessToken())
                        .build());
                // 处理服务端错误
                if(!resp.success()) {
                    log.error("测试结果写入{}失败!",entry.getKey());
                    System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
                            resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), UTF_8)))));
                    continue;
                }
                // 业务数据处理
                log.info(Jsons.DEFAULT.toJson(resp.getData()));
                recordId = resp.getData().getRecord().getRecordId();
            }
            catch (Exception e){
                log.error(e.getMessage(), e);
            }
            if (cloudDocReportDto.isFunctionMode()){
                for (TestResult testResult : cloudDocReportDto.getFunctionTestResults()){
                    String finalRecordId = recordId;
                    CreateAppTableRecordReq detailReq = CreateAppTableRecordReq.newBuilder()
                            .appToken(entry.getValue())
                            .tableId(entry.getKey())
                            .appTableRecord(AppTableRecord.newBuilder()
                                    .fields(new HashMap < String, Object > () {
                                        {
                                            // 映射字段  数据处理，分为 功能测试和冒烟测试，所以需要判断
                                            put("测试模块", testResult.getModuleName());
                                            put("测试案例总数", testResult.getExecutedCases());
                                            put("通过案例数", testResult.getPassCases());
                                            put("未通过案例数", testResult.getFailCases());
                                            put("通过率", testResult.getPassRateAsInt());
                                            put("是否通过", testResult.getConclusion());
                                            put("父记录", new String[]{finalRecordId});
                                        }
                                    })
                                    .build())
                            .build();

                    // 发起请求
                    try {
                        CreateAppTableRecordResp resp = client.bitable().v1().appTableRecord().create(detailReq, RequestOptions.newBuilder()
                                .tenantAccessToken(getTenantAccessToken())
                                .build());
                        // 处理服务端错误
                        if(!resp.success()) {
                            log.error("测试结果写入{}失败!",entry.getKey());
                            System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
                                    resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), UTF_8)))));
                            continue;
                        }
                        // 业务数据处理
                        log.info(Jsons.DEFAULT.toJson(resp.getData()));
                    }catch (Exception e){
                        log.error(e.getMessage(), e);
                    }
                }
            }
            log.info("测试结果写入{}成功!",entry.getKey());
        }
        return true;
    }

    public static void main(String arg[]) throws Exception {
        String startTime = "2024-09-23 18:53:50";
        SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = ft.parse(startTime);
        System.out.println("时间戳: " + date.getTime() / 1000);
        CloudDocumentsService cloudDocumentsService = new CloudDocumentsService();
        cloudDocumentsService.restTemplate = new RestTemplate(); // 手动注入
        String tenantAccessToken = cloudDocumentsService.getTenantAccessToken();
        System.out.println("获取到的Token: " + tenantAccessToken);
        TestResultReportDto cloudDocReportDto = new TestResultReportDto(
                true,
                "2024-09-23 xx:xx:xx",
                DateUtils.getNow(),
                "D:\\FlyTest\\data\\client\\projects\\项目管理\\report\\功能测试\\09_GAC A19 IDC_车身域-指示灯_TestCase_V18测试报告_20240923_18-53-50.xlsx",
                null,
                "E01",
                "SOC_V18",
                "MCU_V18",
                "V18",
                new TestResult(
                        1,
                        "IDC_车身域-指示灯_TestCase_V18测试报告_20240923_18-53)",
                        1,
                        1,
                        100
                ),
                100
        );
        cloudDocumentsService.addNewDataToTestResultTable(cloudDocReportDto);

    }


    /**
     * 添加升级结果数据到飞书表格
     *
     * @return true/false
     */
    public boolean addNewDataToUpgradeResultTable(UpgradeResultReportDto upgradeResultReportDto){
        // 构建client
        Client client = Client.newBuilder(clientId, clientSecret).build();
        Date nowDate;
        //获取当前时间
        String nowStr = DateUtils.getNow();
        try  {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            nowDate = sdf.parse(nowStr); // 转换为 Date 对象
        } catch (Exception e) {
            log.error("日期转换异常: {}", e.getMessage());
            return false;
        }
        Map < String, String > tableToAppTokenMap = getAppTokenAndTableId();
        if (tableToAppTokenMap.isEmpty()){
            log.error("多维表格地址为空！！请先添加多维表格");
            return false;
        }
        for (Map.Entry<String, String> entry : tableToAppTokenMap.entrySet()) {
            // 创建请求对象
            Date finalNowDate = nowDate;
            CreateAppTableRecordReq req = CreateAppTableRecordReq.newBuilder()
                    .appToken(entry.getValue())
                    .tableId(entry.getKey())
                    .appTableRecord(AppTableRecord.newBuilder()
                            .fields(new HashMap < String, Object > () {
                                {
                                    // 映射字段
                                    put("项目名称", clientInfoReceiveFromClient.getProject());
                                    if (upgradeResultReportDto.getUpgradeResultList().size() > 0) {
                                        put("升级①", upgradeResultReportDto.getUpgradeResultList().get(0));
                                    }
                                    if (upgradeResultReportDto.getUpgradeResultList().size() > 1){
                                        put("升级②", upgradeResultReportDto.getUpgradeResultList().get(1));
                                    }
                                    if (upgradeResultReportDto.getUpgradeResultList().size() > 2){
                                        put("升级③", upgradeResultReportDto.getUpgradeResultList().get(2));
                                    }
                                    if (StringUtils.isNotBlank(upgradeResultReportDto.getJenkinsJobUrl())) {
                                        put("jenkins_job_url",upgradeResultReportDto.getJenkinsJobUrl());
                                    }
                                    if (StringUtils.isNotBlank(upgradeResultReportDto.getFilePath())){
                                        put("软件升级包路径",upgradeResultReportDto.getFilePath());
                                    }
                                    put("测试日期", finalNowDate.getTime());
                                    put("判断结果", upgradeResultReportDto.getUpgradeSuccess());
                                }
                            })
                            .build())
                    .build();

            // 发起请求
            try {
                CreateAppTableRecordResp resp = client.bitable().v1().appTableRecord().create(req, RequestOptions.newBuilder()
                        .tenantAccessToken(getTenantAccessToken())
                        .build());
                // 处理服务端错误
                if(!resp.success()) {
                    log.error("升级结果写入{}失败!",entry.getKey());
                    System.out.println(String.format("code:%s,msg:%s,reqId:%s, resp:%s",
                            resp.getCode(), resp.getMsg(), resp.getRequestId(), Jsons.createGSON(true, false).toJson(JsonParser.parseString(new String(resp.getRawResponse().getBody(), UTF_8)))));
                    continue;
                }
                // 业务数据处理
                log.info(Jsons.DEFAULT.toJson(resp.getData()));
                log.info("升级结果写入{}成功!",entry.getKey());
            }
            catch (Exception e){
                log.error(e.getMessage(), e);
            }
        }
        return true;
    }

}
