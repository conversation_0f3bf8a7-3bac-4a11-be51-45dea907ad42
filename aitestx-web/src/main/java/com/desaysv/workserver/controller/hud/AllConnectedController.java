package com.desaysv.workserver.controller.hud;

import com.desaysv.workserver.finder.CameraDeviceFinder;
import com.desaysv.workserver.entity.Device;
import com.desaysv.workserver.manager.DeviceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/allConnectedDevices")
@Lazy
public class AllConnectedController {

    private final DeviceManager deviceManager; // 确保DeviceManager被注入

    @Autowired
    public AllConnectedController(DeviceManager deviceManager) {
        this.deviceManager = deviceManager;
    }

    @GetMapping("/allCameras")
    public List<Device> listCameras() {
        log.info("正在获取所有摄像头");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> cameras = devices.stream() // 使用流来过滤设备
                    .filter(device -> "cameraType".equals(device.getDeviceType())) // 筛选出deviceType为cameraType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个摄像头", cameras.size());
            log.info("摄像头: {}", cameras);
            return cameras; // 返回相机列表
        } catch (Exception e) {
            log.error("获取摄像头失败", e);
            throw new RuntimeException("获取摄像头失败", e);
        }
    }

    @GetMapping("/allCaptures")
    public List<Device> listCaptures() {
        log.info("正在获取所有视频捕获设备");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> videoCaptures = devices.stream() // 使用流来过滤设备
                    .filter(device -> "videoCaptureType".equals(device.getDeviceType())) // 筛选出deviceType为videoCaptureType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个视频捕获设备", videoCaptures.size());
            log.info("视频捕获设备: {}", videoCaptures);
            return videoCaptures; // 返回视频分析仪列表
        } catch (Exception e) {
            log.error("获取视频捕获设备失败", e);
            throw new RuntimeException("获取视频捕获设备失败", e);
        }
    }

    @GetMapping("/allAndroids")
    public List<Device> listAndroids() {
        log.info("正在获取所有安卓设备");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> androids = devices.stream() // 使用流来过滤设备
                    .filter(device -> "androidType".equals(device.getDeviceType())) // 筛选出deviceType为androidType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个安卓设备", androids.size());
            log.info("安卓设备: {}", androids);
            return androids; // 返回安卓设备列表
        } catch (Exception e) {
            log.error("获取安卓设备失败", e);
            throw new RuntimeException("获取安卓设备失败", e);
        }
    }

    @GetMapping("/allUsbSwitches")
    public List<Device> listUsbSwitches() {
        log.info("正在获取所有USB切换板");
        try {
            List<Device> devices = deviceManager.getAllDevices(); // 获取所有设备
            List<Device> usbSwitches = devices.stream() // 使用流来过滤设备
                    .filter(device -> "usbSwitchType".equals(device.getDeviceType())) // 筛选出deviceType为usbSwitchType的设备
                    .collect(Collectors.toList()); // 收集结果到列表

            log.info("成功获取了 {} 个USB切换板", usbSwitches.size());
            log.info("USB切换板: {}", usbSwitches);
            return usbSwitches; // 返回USB转换板设备列表
        } catch (Exception e) {
            log.error("获取USB切换板失败", e);
            throw new RuntimeException("获取USB切换板失败", e);
        }
    }
}