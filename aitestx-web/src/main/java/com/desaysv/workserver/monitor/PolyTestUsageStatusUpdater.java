package com.desaysv.workserver.monitor;

import com.desaysv.workserver.AITestXWebApplication;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.*;
import com.desaysv.workserver.base.operation.base.OperationFailReason;
import com.desaysv.workserver.mail.MailParamDto;
import com.desaysv.workserver.mail.MailUtils;
import com.desaysv.workserver.utils.DateUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 上传PolyTest云平台状态
 */
@Component
@Slf4j
//no lazy
public class PolyTestUsageStatusUpdater implements TestProcessListener, ClientStatusListener, Runnable {
    //    private final BlockingQueue<ClientInfoReceiveFromClient> testClientInfoQueue new LinkedBlockingQueue<>();
    private ClientInfoReceiveFromClient clientInfoReceiveFromClient;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private PolyTestUploadTestSuite upLoadTestSuitePolyTest;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private PolyTestUrls polyTestUrls;
    private LocalDateTime startTestTime;
    private final static int COMPLETE_TO_SEND_EMAIL_MINIMUM_THRESHOLD = 3600;
    private final Executor asyncExecutor;
    private boolean serverAlive = true;
    private ScheduledExecutorService executor;
    private boolean scheduled;

    @Getter
    private final BlockingQueue<ClientPackage> testClientInfoQueue;
    private final List<ClientInfoObserver> observers = new ArrayList<>();

    public PolyTestUsageStatusUpdater() {
        testClientInfoQueue = new LinkedBlockingQueue<>();
        executor = Executors.newSingleThreadScheduledExecutor();
        Executors.newSingleThreadExecutor().execute(this::receiveAndUploadPackage);
        TestProcessManager.addTestProcessListener(this);
        ClientStatusManager.addClientStatusListener(this);
        scheduled = false;
        asyncExecutor = Executors.newSingleThreadExecutor();
    }

    /**
     * 开始周期上传
     */
    public void startSchedule() {
        if (!scheduled) {
            int uploadInterval = 2; //2min
            // 检查执行器状态
            if (executor.isShutdown()) {
                // 如果已关闭,重新创建执行器
                executor = Executors.newSingleThreadScheduledExecutor();
            }
            executor.scheduleAtFixedRate(this, 0, uploadInterval, TimeUnit.MINUTES);
            scheduled = true;
        } else {
            uploadRightNow();
        }
    }

    @Override
    public void run() {
        uploadPackageToServer();
    }

    //    @Override
    public void receiveAndUploadPackage() {
        while (serverAlive) {
            ClientInfoReceiveFromClient clientInfoReceiveFromClient;
            try {
                clientInfoReceiveFromClient = (ClientInfoReceiveFromClient) getTestClientInfoQueue().take();
            } catch (InterruptedException e) {
                log.error(e.getMessage(), e);
                continue;
            }
            uploadUsageStatus(clientInfoReceiveFromClient);
        }
    }

    @Override
    public void userRegister(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient = (ClientInfoReceiveFromClient) clientPackage;
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.OPEN.getValue());
        clientInfoReceiveFromClient.setRemark("");
        clientInfoReceiveFromClient.setTestSuiteId(0);
        startSchedule();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void userLogin(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient = (ClientInfoReceiveFromClient) clientPackage;
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.READY.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void userLogout(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient = (ClientInfoReceiveFromClient) clientPackage;
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.LOGOUT.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void clientExit(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient = (ClientInfoReceiveFromClient) clientPackage;
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.EXITED.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        upLoadTestSuitePolyTest.setTestSuiteId(0);
        uploadRightNow();
        serverAlive = false;
        executor.shutdown();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    public void uploadPackageToServer() {
        getTestClientInfoQueue().add(clientInfoReceiveFromClient);
    }

    public void uploadRightNow() {
        getTestClientInfoQueue().add((ClientInfoReceiveFromClient) clientInfoReceiveFromClient.clone());
    }

    public void uploadUsageStatus(ClientInfoReceiveFromClient clientInfoReceiveFromClient) {
        if (clientInfoReceiveFromClient.isOfflineMode()) {
//            log.info("离线模式，不上传状态");
            return;
        }
        PolyTestUsageStatus polyTestUsageStatus = new PolyTestUsageStatus();
        String[] testUnitArr = clientInfoReceiveFromClient.getTestUnit().split("/");
        polyTestUsageStatus.setAddr(clientInfoReceiveFromClient.getWorkstation());
        polyTestUsageStatus.setEndTime("");
        polyTestUsageStatus.setPcUuid(CommandUtils.getPcUUID());
        polyTestUsageStatus.setPcAppRunId(CommandUtils.getAppPID());
        polyTestUsageStatus.setToolVersion(AITestXWebApplication.getServerVersion());
        polyTestUsageStatus.setIpAddr(clientInfoReceiveFromClient.getIpAddress());
        polyTestUsageStatus.setPcName(testUnitArr[0]);
        polyTestUsageStatus.setPeripherals("");
        polyTestUsageStatus.setRemark(clientInfoReceiveFromClient.getRemark());
        polyTestUsageStatus.setStartTime(clientInfoReceiveFromClient.getStartTime());
        polyTestUsageStatus.setStatus(clientInfoReceiveFromClient.getStatusCode());
        polyTestUsageStatus.setTestSuiteId(upLoadTestSuitePolyTest.getTestSuiteId());
        polyTestUsageStatus.setToolId(clientInfoReceiveFromClient.getPlatformCode());
        polyTestUsageStatus.setUserId(clientInfoReceiveFromClient.getUserId());
        try {
            ResponseEntity<LargeDataUploadAPIResponse> result = restTemplate.exchange(
                    polyTestUrls.UPLOAD_USAGE_STATUS_URL,
                    HttpMethod.POST,
                    new HttpEntity<>(polyTestUsageStatus),
                    LargeDataUploadAPIResponse.class);
            LargeDataUploadAPIResponse response = result.getBody();
            if (response != null && response.getCode() == 20000) {
                log.debug("上传客户端状态成功->状态代码:{}，上传信息:{}，云平台返回结果:{}", polyTestUsageStatus.getStatus(), polyTestUsageStatus, response);
            } else {
                log.warn("上传客户端状态失败->网址:{},状态代码:{}，上传信息:{}，云平台返回结果:{}", polyTestUrls.UPLOAD_USAGE_STATUS_URL, polyTestUsageStatus.getStatus(), polyTestUsageStatus, response);
            }
        } catch (Exception e) {
            String statusName = PolyTestStatusConstant.getStatusNameByCode(clientInfoReceiveFromClient.getStatusCode());
            log.error("上传客户端状态失败->网址:{},状态代码:{}>>{}", polyTestUrls.UPLOAD_USAGE_STATUS_URL, statusName, e.getMessage());
            log.error("上传信息:{}", polyTestUsageStatus);
            log.debug("上传客户端状态失败:", e);
        }
    }

    @Override
    public void testSuiteStart(ExecutionContext executionContext) {
        startTestTime = LocalDateTime.now();
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.TESTING.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testcaseStart(ExecutionContext executionContext) {
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.TESTING.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testFailed(ExecutionContext executionContext, int testCycle, OperationFailReason reason) {
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.FAILED.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        String failMessage = reason.getFailMessage();
        String truncatedReason = failMessage != null
                ? failMessage.length() > 250
                ? failMessage.substring(0, 250)
                : failMessage
                : "";
        clientInfoReceiveFromClient.setRemark(truncatedReason);
        uploadRightNow();
        if (!executionContext.isDebugModeEnabled()) {
            sendEmail(executionContext.isEnableSendingEmail(), getFailMailParamDto(reason.getWholeMessage()));
        }
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testPausing() {
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.PAUSING.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testResume() {
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.TESTING.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testComplete(ExecutionContext executionContext, boolean isFailed, boolean isSendEmail) {
        clientInfoReceiveFromClient.setStatusCode(isFailed ? PolyTestStatusConstant.Status.COMPLETED_FAIL.getValue() :
                PolyTestStatusConstant.Status.COMPLETED_PASS.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        if (Duration.between(startTestTime, LocalDateTime.now()).getSeconds() >
                COMPLETE_TO_SEND_EMAIL_MINIMUM_THRESHOLD  && isSendEmail) {
            log.info("发送测试完成邮件");
            sendEmail(executionContext.isEnableSendingEmail(), getCompletedMailParamDto());
        }
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void testTerminated() {
        clientInfoReceiveFromClient.setStatusCode(PolyTestStatusConstant.Status.MANUALLY_TERMINATED.getValue());
        clientInfoReceiveFromClient.setStartTime(DateUtils.getNow());
        clientInfoReceiveFromClient.setRemark("");
        uploadRightNow();
        updateClientInfo(clientInfoReceiveFromClient);
    }

    private void sendEmail(boolean enabled, MailParamDto mailParamDto) {
        if (enabled) {
            asyncExecutor.execute(() -> mailUtils.sendEmail(mailParamDto));
        } else {
            log.warn("邮件通知已被禁用，当前发送主题：{}", mailParamDto.getSubject());
        }
    }

    private MailParamDto getFailMailParamDto(String reason) {
        String subject = String.format("%s/%s-%s-%s测试暂停",
                clientInfoReceiveFromClient.getProject(),
                clientInfoReceiveFromClient.getUserName(),
                clientInfoReceiveFromClient.getClient(),
                clientInfoReceiveFromClient.getTestUnit());
        MailParamDto mailParamDto = new MailParamDto();
        mailParamDto.setContent(String.format("<html>%s(%s)测试已暂停，请及时处理<br><br>%s</html>",
                clientInfoReceiveFromClient.getClient(), clientInfoReceiveFromClient.getTestUnit(), reason).replaceAll("\n", "<br>"));
        // 邮件接口recipient和recipients有冲突 同时设置时只会发recipient 导致这里需要把邮箱地址放到recipients中
        // mailParamDto.setRecipient(clientInfoReceiveFromClient.getEmail());
        //只发给测试脚本的邮箱
        String[] emails = clientInfoReceiveFromClient.getScriptEmails();
        if (emails == null) {
            emails = new String[0];
        }
        String[] allEmails = new String[emails.length + 1];
        System.arraycopy(emails, 0, allEmails, 0, emails.length);
        allEmails[emails.length] = clientInfoReceiveFromClient.getEmail();
        mailParamDto.setRecipients(allEmails);
        mailParamDto.setSubject(subject);
        return mailParamDto;
    }

    private MailParamDto getCompletedMailParamDto() {
        String subject = String.format("%s/%s-%s-%s测试完成",
                clientInfoReceiveFromClient.getProject(),
                clientInfoReceiveFromClient.getUserName(),
                clientInfoReceiveFromClient.getClient(),
                clientInfoReceiveFromClient.getTestUnit());
        MailParamDto mailParamDto = new MailParamDto();
        mailParamDto.setContent(String.format("%s(%s)测试已完成，请及时跟进", clientInfoReceiveFromClient.getClient(), clientInfoReceiveFromClient.getTestUnit()));
        // 邮件接口recipient和recipients有冲突 同时设置时只会发recipient 导致这里需要把邮箱地址放到recipients中
        // mailParamDto.setRecipient(clientInfoReceiveFromClient.getEmail());
        //只发给测试脚本的邮箱
        String[] emails = clientInfoReceiveFromClient.getScriptEmails();
        if (emails == null) {
            emails = new String[0];
        }
        String[] allEmails = new String[emails.length + 1];
        System.arraycopy(emails, 0, allEmails, 0, emails.length);
        allEmails[emails.length] = clientInfoReceiveFromClient.getEmail();
        mailParamDto.setRecipients(allEmails);
        mailParamDto.setSubject(subject);
        return mailParamDto;
    }

    @Override
    public void configurationUserEmails(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient.setEmails(((ClientInfoReceiveFromClient) clientPackage).getEmails());
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void configurationUserScriptEmails(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient.setScriptEmails(((ClientInfoReceiveFromClient) clientPackage).getScriptEmails());
        updateClientInfo(clientInfoReceiveFromClient);
    }

    @Override
    public void configurationUserRobotUrls(ClientPackage clientPackage) {
        this.clientInfoReceiveFromClient.setUrls(((ClientInfoReceiveFromClient) clientPackage).getUrls());
        //顺便保存一下多维表格信息
        this.clientInfoReceiveFromClient.setMultiTableUrls(((ClientInfoReceiveFromClient) clientPackage).getMultiTableUrls());
        updateClientInfo(clientInfoReceiveFromClient);
    }

    public void addObserver(ClientInfoObserver observer) {
        observers.add(observer);
    }

    private void notifyObservers(ClientInfoReceiveFromClient clientInfo) {
        for (ClientInfoObserver observer : observers) {
            observer.update(clientInfo);
        }
    }

    public void updateClientInfo(ClientInfoReceiveFromClient newClientInfo) {
        notifyObservers(newClientInfo);
    }
}
