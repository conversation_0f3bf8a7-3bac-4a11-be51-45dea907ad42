package com.desaysv.workserver.monitor;

import com.desaysv.workserver.AITestXWebApplication;
import com.desaysv.workserver.TestCaseListener;
import com.desaysv.workserver.base.context.ExecutionContext;
import com.desaysv.workserver.base.manager.TestProcessListener;
import com.desaysv.workserver.base.manager.TestProcessManager;
import com.desaysv.workserver.base.manager.testSuite.TestCaseExecuteInfo;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfo;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfoListener;
import com.desaysv.workserver.base.manager.testSuite.TestSuiteInfoManager;
import com.desaysv.workserver.utils.DateUtils;
import com.desaysv.workserver.utils.command.CommandUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
//no lazy
public class PolyTestUploadTestSuite implements TestSuiteInfoListener, TestProcessListener {

    @Autowired
    private RestTemplate restTemplate;
    @Getter
    @Setter
    private int testSuiteId;
    @Autowired
    private PolyTestUrls polyTestUrls;

    public PolyTestUploadTestSuite() {
        TestSuiteInfoManager.addTestSuiteInfoListener(this);
        TestProcessManager.addTestProcessListener(this);
    }

    @Override
    public void startTestSuite(TestSuiteInfo testSuiteInfo) {
        long sTime = System.currentTimeMillis();
        testSuiteInfo.setPcUuid(CommandUtils.getPcUUID());
        testSuiteInfo.setPcAppRunId(CommandUtils.getAppPID());
        testSuiteInfo.setToolVersion(AITestXWebApplication.getServerVersion());
        testSuiteInfo.setStartTime(DateUtils.getNow());
        log.info("开始启动测试集合:{}", testSuiteInfo.getInfoWithoutTestCases());
        try {
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
            ResponseEntity<LargeDataUploadAPIResponse> result = restTemplate.exchange(
                    polyTestUrls.START_TEST_SUITE_URL,
                    HttpMethod.POST,
                    new HttpEntity<>(testSuiteInfo),
                    LargeDataUploadAPIResponse.class);
            LargeDataUploadAPIResponse response = result.getBody();
            if (response != null) {
                if (response.getData() == null) {
                    throw new IllegalArgumentException(String.format("云平台启动测试集合返回结果:%s，网址:%s", response, polyTestUrls.START_TEST_SUITE_URL));
                }
                testSuiteId = response.getData().getTestSuiteId();
                log.info("testSuiteId:{}，启动测试集合耗时:{}ms， 测试集合：{}", testSuiteId, System.currentTimeMillis() - sTime, testSuiteInfo.getInfoWithoutTestCases());
            } else {
                log.info("测试集合启动失败，返回为空");
                throw new IllegalStateException("测试集合启动失败，返回为空");
            }
//            System.out.println("testSuiteId---" + testSuiteId);
        } catch (Exception e) {
            log.error("上传到启动测试集合接口失败:{}", e.getMessage());
            log.debug("上传到启动测试集合接口失败:", e);
            throw e;
        }
    }

    @Override
    public void finishSingleCaseTesting(TestCaseExecuteInfo testCaseExecuteInfo) {
        executeTestCase(testCaseExecuteInfo);
    }

    /**
     * 上传单个用例执行结果
     *
     * @param testCaseExecuteInfo 用例执行结果
     */
    private void executeTestCase(TestCaseExecuteInfo testCaseExecuteInfo) {
        testCaseExecuteInfo.setTestSuiteId(testSuiteId);
        testCaseExecuteInfo.setEndTime(DateUtils.getNow());
        try {
            ResponseEntity<LargeDataUploadAPIResponse> result = restTemplate.exchange(
                    polyTestUrls.EXE_TEST_CASE_URL,
                    HttpMethod.POST,
                    new HttpEntity<>(testCaseExecuteInfo),
                    LargeDataUploadAPIResponse.class);
            LargeDataUploadAPIResponse response = result.getBody();
            log.debug("上传到测试用例执行接口返回:{}", response);
        } catch (Exception e) {
            log.error("上传到测试用例执行接口失败:{}", e.getMessage());
            log.debug("上传到测试用例执行接口失败:", e);
        }
    }

    @Override
    public void testcaseStart(ExecutionContext executionContext) {
        //TODO：上传云平台Case信息
//        TestCaseExecuteInfo testCaseExecuteInfo = new TestCaseExecuteInfo();
//        int actualResult = PolyTestStatusConstant.Status.TESTING.getValue();
//        testCaseExecuteInfo.setActualResult(actualResult);
    }
}
